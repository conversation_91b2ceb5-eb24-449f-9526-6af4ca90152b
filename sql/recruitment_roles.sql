-- 招聘平台角色权限配置

-- 创建招聘平台相关角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
('招聘管理员', 'recruitment_admin', 3, '1', 1, 1, '0', '0', 'admin', sysdate(), '', NULL, '招聘平台管理员，拥有所有招聘相关权限'),
('商家用户', 'merchant_user', 4, '2', 1, 1, '0', '0', 'admin', sysdate(), '', NULL, '商家用户，可以发布职位和管理申请'),
('求职用户', 'job_seeker', 5, '5', 1, 1, '0', '0', 'admin', sysdate(), '', NULL, '求职用户，可以浏览职位和投递简历');

-- 获取角色ID
SET @recruitment_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'recruitment_admin');
SET @merchant_user_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'merchant_user');
SET @job_seeker_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'job_seeker');

-- 获取招聘管理主菜单ID
SET @recruitment_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '招聘管理' AND parent_id = 0);

-- 为招聘管理员分配所有招聘相关权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @recruitment_admin_role_id, menu_id 
FROM sys_menu 
WHERE menu_id = @recruitment_menu_id 
   OR parent_id = @recruitment_menu_id 
   OR parent_id IN (SELECT menu_id FROM sys_menu WHERE parent_id = @recruitment_menu_id);

-- 为商家用户分配部分权限（职位管理、申请管理、订单查看）
-- 商家用户可以访问的菜单
SET @merchant_menus = (
    SELECT GROUP_CONCAT(menu_id) 
    FROM sys_menu 
    WHERE menu_name IN ('招聘管理', '职位管理', '求职申请', '订单管理') 
       OR (parent_id IN (
           SELECT menu_id FROM sys_menu 
           WHERE menu_name IN ('职位管理', '求职申请', '订单管理')
       ) AND menu_type = 'F' AND perms REGEXP '(query|list|add|edit|view|communicate|reject)')
);

-- 为商家用户分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @merchant_user_role_id, menu_id 
FROM sys_menu 
WHERE menu_name = '招聘管理' AND parent_id = 0;

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @merchant_user_role_id, menu_id 
FROM sys_menu 
WHERE menu_name IN ('职位管理', '求职申请', '订单管理') 
   AND parent_id = @recruitment_menu_id;

-- 商家用户职位管理权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @merchant_user_role_id, menu_id 
FROM sys_menu 
WHERE parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '职位管理' AND parent_id = @recruitment_menu_id)
   AND perms IN ('recruitment:job:query', 'recruitment:job:add', 'recruitment:job:edit', 'recruitment:job:remove', 'recruitment:job:export');

-- 商家用户申请管理权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @merchant_user_role_id, menu_id 
FROM sys_menu 
WHERE parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '求职申请' AND parent_id = @recruitment_menu_id)
   AND perms IN ('recruitment:jobApplication:query', 'recruitment:jobApplication:view', 'recruitment:jobApplication:communicate', 'recruitment:jobApplication:reject', 'recruitment:jobApplication:merchant', 'recruitment:jobApplication:job');

-- 商家用户订单查看权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @merchant_user_role_id, menu_id 
FROM sys_menu 
WHERE parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '订单管理' AND parent_id = @recruitment_menu_id)
   AND perms IN ('recruitment:order:query');

-- 为求职用户分配基础权限（主要是查看权限）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @job_seeker_role_id, menu_id 
FROM sys_menu 
WHERE menu_name = '招聘管理' AND parent_id = 0;

-- 求职用户只能查看职位和管理自己的申请
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @job_seeker_role_id, menu_id 
FROM sys_menu 
WHERE menu_name IN ('职位管理', '求职申请', '订单管理') 
   AND parent_id = @recruitment_menu_id;

-- 求职用户职位查看权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @job_seeker_role_id, menu_id 
FROM sys_menu 
WHERE parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '职位管理' AND parent_id = @recruitment_menu_id)
   AND perms IN ('recruitment:job:query');

-- 求职用户申请管理权限（只能查看自己的申请）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @job_seeker_role_id, menu_id 
FROM sys_menu 
WHERE parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '求职申请' AND parent_id = @recruitment_menu_id)
   AND perms IN ('recruitment:jobApplication:query');

-- 求职用户订单管理权限（只能查看自己的订单）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @job_seeker_role_id, menu_id 
FROM sys_menu 
WHERE parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '订单管理' AND parent_id = @recruitment_menu_id)
   AND perms IN ('recruitment:order:query');

-- 创建招聘平台字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
('招聘用户类型', 'recruitment_user_type', '0', 'admin', sysdate(), '', NULL, '招聘平台用户类型'),
('职位状态', 'recruitment_job_status', '0', 'admin', sysdate(), '', NULL, '职位发布状态'),
('申请状态', 'recruitment_application_status', '0', 'admin', sysdate(), '', NULL, '求职申请状态'),
('订单类型', 'recruitment_order_type', '0', 'admin', sysdate(), '', NULL, '订单类型'),
('支付状态', 'recruitment_pay_status', '0', 'admin', sysdate(), '', NULL, '支付状态'),
('退款状态', 'recruitment_refund_status', '0', 'admin', sysdate(), '', NULL, '退款状态'),
('消费类型', 'recruitment_consumption_type', '0', 'admin', sysdate(), '', NULL, '消费记录类型'),
('商家审核状态', 'recruitment_merchant_audit_status', '0', 'admin', sysdate(), '', NULL, '商家审核状态'),
('VIP状态', 'recruitment_vip_status', '0', 'admin', sysdate(), '', NULL, 'VIP会员状态');

-- 创建招聘平台字典数据
-- 招聘用户类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '求职者', '1', 'recruitment_user_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '求职者用户'),
(2, '商家', '2', 'recruitment_user_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '商家用户'),
(3, '管理员', '3', 'recruitment_user_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '管理员用户');

-- 职位状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '草稿', '0', 'recruitment_job_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', NULL, '草稿状态'),
(2, '待审核', '1', 'recruitment_job_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '待审核状态'),
(3, '已发布', '2', 'recruitment_job_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '已发布状态'),
(4, '已下线', '3', 'recruitment_job_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '已下线状态');

-- 申请状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '待处理', '0', 'recruitment_application_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', NULL, '待处理状态'),
(2, '已查看', '1', 'recruitment_application_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', NULL, '已查看状态'),
(3, '已沟通', '2', 'recruitment_application_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '已沟通状态'),
(4, '已拒绝', '3', 'recruitment_application_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '已拒绝状态');

-- 订单类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '查看联系方式', '1', 'recruitment_order_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '查看联系方式订单'),
(2, 'VIP会员', '2', 'recruitment_order_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, 'VIP会员订单');

-- 支付状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '待支付', '0', 'recruitment_pay_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', NULL, '待支付状态'),
(2, '已支付', '1', 'recruitment_pay_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '已支付状态'),
(3, '已退款', '2', 'recruitment_pay_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '已退款状态');

-- 退款状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '无退款', '0', 'recruitment_refund_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', NULL, '无退款'),
(2, '退款中', '1', 'recruitment_refund_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '退款处理中'),
(3, '已退款', '2', 'recruitment_refund_status', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '已完成退款');

-- 消费类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '查看联系方式', '1', 'recruitment_consumption_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '查看联系方式消费'),
(2, '购买VIP', '2', 'recruitment_consumption_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '购买VIP消费');

-- 商家审核状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '待审核', '0', 'recruitment_merchant_audit_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', NULL, '待审核状态'),
(2, '审核通过', '1', 'recruitment_merchant_audit_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '审核通过状态'),
(3, '审核拒绝', '2', 'recruitment_merchant_audit_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '审核拒绝状态');

-- VIP状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '普通用户', '0', 'recruitment_vip_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', NULL, '普通用户'),
(2, 'VIP用户', '1', 'recruitment_vip_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, 'VIP会员用户');
