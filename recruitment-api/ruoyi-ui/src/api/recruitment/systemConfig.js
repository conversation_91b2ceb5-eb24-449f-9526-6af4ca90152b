import request from '@/utils/request'

// 查询系统配置列表
export function listSystemConfig(query) {
  return request({
    url: '/recruitment/systemConfig/list',
    method: 'get',
    params: query
  })
}

// 查询系统配置详细
export function getSystemConfig(configId) {
  return request({
    url: '/recruitment/systemConfig/' + configId,
    method: 'get'
  })
}

// 新增系统配置
export function addSystemConfig(data) {
  return request({
    url: '/recruitment/systemConfig',
    method: 'post',
    data: data
  })
}

// 修改系统配置
export function updateSystemConfig(data) {
  return request({
    url: '/recruitment/systemConfig',
    method: 'put',
    data: data
  })
}

// 删除系统配置
export function delSystemConfig(configId) {
  return request({
    url: '/recruitment/systemConfig/' + configId,
    method: 'delete'
  })
}

// 根据配置键获取配置值
export function getConfigByKey(configKey) {
  return request({
    url: '/recruitment/systemConfig/key/' + configKey,
    method: 'get'
  })
}

// 批量更新配置
export function batchUpdateConfig(data) {
  return request({
    url: '/recruitment/systemConfig/batch',
    method: 'put',
    data: data
  })
}

// 获取价格配置
export function getPriceConfig() {
  return request({
    url: '/recruitment/systemConfig/price',
    method: 'get'
  })
}

// 更新价格配置
export function updatePriceConfig(data) {
  return request({
    url: '/recruitment/systemConfig/price',
    method: 'put',
    data: data
  })
}

// 初始化默认配置
export function initDefaultConfig() {
  return request({
    url: '/recruitment/systemConfig/init',
    method: 'post'
  })
}

// 重置配置为默认值
export function resetConfig(configKey) {
  return request({
    url: '/recruitment/systemConfig/reset/' + configKey,
    method: 'post'
  })
}

// 获取所有配置分组
export function getConfigGroups() {
  return request({
    url: '/recruitment/systemConfig/groups',
    method: 'get'
  })
}

// 根据分组获取配置列表
export function getConfigsByGroup(groupName) {
  return request({
    url: '/recruitment/systemConfig/group/' + groupName,
    method: 'get'
  })
}
