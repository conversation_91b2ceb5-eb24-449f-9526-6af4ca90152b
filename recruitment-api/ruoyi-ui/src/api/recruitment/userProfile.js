import request from '@/utils/request'

// 查询用户扩展表列表
export function listUserProfile(query) {
  return request({
    url: '/recruitment/userProfile/list',
    method: 'get',
    params: query
  })
}

// 查询用户扩展表详细
export function getUserProfile(profileId) {
  return request({
    url: '/recruitment/userProfile/' + profileId,
    method: 'get'
  })
}

// 新增用户扩展表
export function addUserProfile(data) {
  return request({
    url: '/recruitment/userProfile',
    method: 'post',
    data: data
  })
}

// 修改用户扩展表
export function updateUserProfile(data) {
  return request({
    url: '/recruitment/userProfile',
    method: 'put',
    data: data
  })
}

// 删除用户扩展表
export function delUserProfile(profileId) {
  return request({
    url: '/recruitment/userProfile/' + profileId,
    method: 'delete'
  })
}

// 获取当前用户档案
export function getProfile() {
  return request({
    url: '/recruitment/userProfile/profile',
    method: 'get'
  })
}

// 更新当前用户档案
export function updateProfile(data) {
  return request({
    url: '/recruitment/userProfile/profile',
    method: 'put',
    data: data
  })
}

// 冻结用户账户
export function freezeUser(userId) {
  return request({
    url: '/recruitment/userProfile/freeze/' + userId,
    method: 'put'
  })
}

// 解冻用户账户
export function unfreezeUser(userId) {
  return request({
    url: '/recruitment/userProfile/unfreeze/' + userId,
    method: 'put'
  })
}

// 查询VIP用户列表
export function getVipUsers() {
  return request({
    url: '/recruitment/userProfile/vip',
    method: 'get'
  })
}

// 查询即将过期的VIP用户
export function getExpiringSoonVipUsers(days) {
  return request({
    url: '/recruitment/userProfile/vip/expiring/' + days,
    method: 'get'
  })
}

// 检查当前用户是否为VIP
export function checkVip() {
  return request({
    url: '/recruitment/userProfile/vip/check',
    method: 'get'
  })
}

// 手动处理过期VIP用户
export function processExpiredVipUsers() {
  return request({
    url: '/recruitment/userProfile/vip/processExpired',
    method: 'post'
  })
}

// 根据用户ID查询用户档案
export function getUserProfileByUserId(userId) {
  return request({
    url: '/recruitment/userProfile/user/' + userId,
    method: 'get'
  })
}

// 更新用户VIP状态
export function updateVipStatus(userId, data) {
  return request({
    url: '/recruitment/userProfile/vip/' + userId,
    method: 'put',
    data: data
  })
}
