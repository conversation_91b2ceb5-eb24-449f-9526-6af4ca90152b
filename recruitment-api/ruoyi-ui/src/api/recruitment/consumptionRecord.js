import request from '@/utils/request'

// 查询消费记录表列表
export function listConsumptionRecord(query) {
  return request({
    url: '/recruitment/consumptionRecord/list',
    method: 'get',
    params: query
  })
}

// 查询消费记录表详细
export function getConsumptionRecord(recordId) {
  return request({
    url: '/recruitment/consumptionRecord/' + recordId,
    method: 'get'
  })
}

// 新增消费记录表
export function addConsumptionRecord(data) {
  return request({
    url: '/recruitment/consumptionRecord',
    method: 'post',
    data: data
  })
}

// 修改消费记录表
export function updateConsumptionRecord(data) {
  return request({
    url: '/recruitment/consumptionRecord',
    method: 'put',
    data: data
  })
}

// 删除消费记录表
export function delConsumptionRecord(recordId) {
  return request({
    url: '/recruitment/consumptionRecord/' + recordId,
    method: 'delete'
  })
}

// 获取当前用户的消费记录列表
export function getMyConsumptionRecords() {
  return request({
    url: '/recruitment/consumptionRecord/my',
    method: 'get'
  })
}

// 根据用户ID查询消费记录列表
export function getConsumptionRecordsByUser(userId) {
  return request({
    url: '/recruitment/consumptionRecord/user/' + userId,
    method: 'get'
  })
}

// 统计用户消费总额
export function sumUserConsumption(userId) {
  return request({
    url: '/recruitment/consumptionRecord/sum/user/' + userId,
    method: 'get'
  })
}

// 统计用户某类型消费总额
export function sumUserConsumptionByType(userId, consumptionType) {
  return request({
    url: '/recruitment/consumptionRecord/sum/user/' + userId + '/type/' + consumptionType,
    method: 'get'
  })
}

// 统计用户消费次数
export function countUserConsumption(userId) {
  return request({
    url: '/recruitment/consumptionRecord/count/user/' + userId,
    method: 'get'
  })
}

// 统计某类型消费次数
export function countUserConsumptionByType(userId, consumptionType) {
  return request({
    url: '/recruitment/consumptionRecord/count/user/' + userId + '/type/' + consumptionType,
    method: 'get'
  })
}

// 查询最近消费记录
export function getRecentConsumptionRecords(userId, limit) {
  return request({
    url: '/recruitment/consumptionRecord/recent/user/' + userId + '/' + limit,
    method: 'get'
  })
}

// 统计当前用户消费总额
export function sumMyConsumption() {
  return request({
    url: '/recruitment/consumptionRecord/sum/my',
    method: 'get'
  })
}

// 统计当前用户某类型消费总额
export function sumMyConsumptionByType(consumptionType) {
  return request({
    url: '/recruitment/consumptionRecord/sum/my/type/' + consumptionType,
    method: 'get'
  })
}

// 统计当前用户消费次数
export function countMyConsumption() {
  return request({
    url: '/recruitment/consumptionRecord/count/my',
    method: 'get'
  })
}

// 统计当前用户某类型消费次数
export function countMyConsumptionByType(consumptionType) {
  return request({
    url: '/recruitment/consumptionRecord/count/my/type/' + consumptionType,
    method: 'get'
  })
}

// 查询当前用户最近消费记录
export function getMyRecentConsumptionRecords(limit) {
  return request({
    url: '/recruitment/consumptionRecord/recent/my/' + limit,
    method: 'get'
  })
}
