import request from '@/utils/request'

// 查询订单表列表
export function listOrder(query) {
  return request({
    url: '/recruitment/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单表详细
export function getOrder(orderId) {
  return request({
    url: '/recruitment/order/' + orderId,
    method: 'get'
  })
}

// 根据订单号查询订单
export function getOrderByOrderNo(orderNo) {
  return request({
    url: '/recruitment/order/orderNo/' + orderNo,
    method: 'get'
  })
}

// 新增订单表
export function addOrder(data) {
  return request({
    url: '/recruitment/order',
    method: 'post',
    data: data
  })
}

// 修改订单表
export function updateOrder(data) {
  return request({
    url: '/recruitment/order',
    method: 'put',
    data: data
  })
}

// 删除订单表
export function delOrder(orderId) {
  return request({
    url: '/recruitment/order/' + orderId,
    method: 'delete'
  })
}

// 创建查看联系方式订单
export function createViewContactOrder(jobId) {
  return request({
    url: '/recruitment/order/createViewContact/' + jobId,
    method: 'post'
  })
}

// 创建VIP会员订单
export function createVipOrder(vipType) {
  return request({
    url: '/recruitment/order/createVip/' + vipType,
    method: 'post'
  })
}

// 支付订单
export function payOrder(orderNo, data) {
  return request({
    url: '/recruitment/order/pay/' + orderNo,
    method: 'post',
    data: data
  })
}

// 取消订单
export function cancelOrder(orderId) {
  return request({
    url: '/recruitment/order/cancel/' + orderId,
    method: 'put'
  })
}

// 申请退款
export function applyRefund(orderId, data) {
  return request({
    url: '/recruitment/order/refund/' + orderId,
    method: 'put',
    data: data
  })
}

// 处理退款（管理员）
export function processRefund(orderId, data) {
  return request({
    url: '/recruitment/order/processRefund/' + orderId,
    method: 'put',
    data: data
  })
}

// 获取当前用户的订单列表
export function getMyOrders() {
  return request({
    url: '/recruitment/order/my',
    method: 'get'
  })
}

// 查询待支付订单
export function getPendingPaymentOrders() {
  return request({
    url: '/recruitment/order/pending',
    method: 'get'
  })
}

// 查询已支付订单
export function getPaidOrders() {
  return request({
    url: '/recruitment/order/paid',
    method: 'get'
  })
}

// 查询退款订单
export function getRefundOrders() {
  return request({
    url: '/recruitment/order/refund',
    method: 'get'
  })
}

// 统计用户订单数量
export function countUserOrders(userId) {
  return request({
    url: '/recruitment/order/count/user/' + userId,
    method: 'get'
  })
}

// 统计用户消费金额
export function sumUserOrderAmount(userId) {
  return request({
    url: '/recruitment/order/amount/user/' + userId,
    method: 'get'
  })
}

// 统计当前用户订单数量
export function countMyOrders() {
  return request({
    url: '/recruitment/order/count/my',
    method: 'get'
  })
}

// 统计当前用户消费金额
export function sumMyOrderAmount() {
  return request({
    url: '/recruitment/order/amount/my',
    method: 'get'
  })
}

// 自动取消过期订单
export function autoCancelExpiredOrders() {
  return request({
    url: '/recruitment/order/autoCancelExpired',
    method: 'post'
  })
}
