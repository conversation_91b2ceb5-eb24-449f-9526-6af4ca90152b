import request from '@/utils/request'

// 查询求职申请表列表
export function listJobApplication(query) {
  return request({
    url: '/recruitment/jobApplication/list',
    method: 'get',
    params: query
  })
}

// 查询求职申请表详细
export function getJobApplication(applicationId) {
  return request({
    url: '/recruitment/jobApplication/' + applicationId,
    method: 'get'
  })
}

// 新增求职申请表
export function addJobApplication(data) {
  return request({
    url: '/recruitment/jobApplication',
    method: 'post',
    data: data
  })
}

// 修改求职申请表
export function updateJobApplication(data) {
  return request({
    url: '/recruitment/jobApplication',
    method: 'put',
    data: data
  })
}

// 删除求职申请表
export function delJobApplication(applicationId) {
  return request({
    url: '/recruitment/jobApplication/' + applicationId,
    method: 'delete'
  })
}

// 用户申请职位
export function applyJob(jobId) {
  return request({
    url: '/recruitment/jobApplication/apply/' + jobId,
    method: 'post'
  })
}

// 检查用户是否已申请该职位
export function checkApplication(jobId) {
  return request({
    url: '/recruitment/jobApplication/check/' + jobId,
    method: 'get'
  })
}

// 获取当前用户的申请列表
export function getMyApplications() {
  return request({
    url: '/recruitment/jobApplication/my',
    method: 'get'
  })
}

// 根据商家ID查询申请列表
export function getApplicationsByMerchant(merchantId) {
  return request({
    url: '/recruitment/jobApplication/merchant/' + merchantId,
    method: 'get'
  })
}

// 根据职位ID查询申请列表
export function getApplicationsByJob(jobId) {
  return request({
    url: '/recruitment/jobApplication/job/' + jobId,
    method: 'get'
  })
}

// 商家查看申请
export function viewApplication(applicationId) {
  return request({
    url: '/recruitment/jobApplication/view/' + applicationId,
    method: 'put'
  })
}

// 商家沟通申请者
export function communicateApplicant(applicationId, data) {
  return request({
    url: '/recruitment/jobApplication/communicate/' + applicationId,
    method: 'put',
    data: data
  })
}

// 商家拒绝申请
export function rejectApplication(applicationId, data) {
  return request({
    url: '/recruitment/jobApplication/reject/' + applicationId,
    method: 'put',
    data: data
  })
}

// 统计用户申请次数
export function countUserApplications(userId) {
  return request({
    url: '/recruitment/jobApplication/count/user/' + userId,
    method: 'get'
  })
}

// 统计职位申请次数
export function countJobApplications(jobId) {
  return request({
    url: '/recruitment/jobApplication/count/job/' + jobId,
    method: 'get'
  })
}

// 统计商家收到的申请次数
export function countMerchantApplications(merchantId) {
  return request({
    url: '/recruitment/jobApplication/count/merchant/' + merchantId,
    method: 'get'
  })
}

// 统计当前用户申请次数
export function countMyApplications() {
  return request({
    url: '/recruitment/jobApplication/count/my',
    method: 'get'
  })
}
