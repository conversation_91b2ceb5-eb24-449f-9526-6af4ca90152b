<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="真实姓名" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入真实姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="VIP状态" prop="isVip">
        <el-select v-model="queryParams.isVip" placeholder="请选择VIP状态" clearable>
          <el-option label="非VIP" value="0" />
          <el-option label="VIP用户" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="冻结" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['recruitment:userProfile:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['recruitment:userProfile:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['recruitment:userProfile:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['recruitment:userProfile:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userProfileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户名" align="center" prop="userName" />
      <el-table-column label="真实姓名" align="center" prop="realName" />
      <el-table-column label="手机号" align="center" prop="phonenumber" />
      <el-table-column label="邮箱" align="center" prop="email" />
      <el-table-column label="VIP状态" align="center" prop="isVip">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isVip"/>
        </template>
      </el-table-column>
      <el-table-column label="VIP到期时间" align="center" prop="vipExpireTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.vipExpireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="查看联系方式次数" align="center" prop="viewContactCount" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['recruitment:userProfile:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['recruitment:userProfile:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.status == '0'"
            size="mini"
            type="text"
            icon="el-icon-lock"
            @click="handleFreeze(scope.row)"
            v-hasPermi="['recruitment:userProfile:freeze']"
          >冻结</el-button>
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-unlock"
            @click="handleUnfreeze(scope.row)"
            v-hasPermi="['recruitment:userProfile:unfreeze']"
          >解冻</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['recruitment:userProfile:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户扩展表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker clearable
            v-model="form.birthDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出生日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学历" prop="education">
          <el-input v-model="form.education" placeholder="请输入学历" />
        </el-form-item>
        <el-form-item label="工作经验" prop="workExperience">
          <el-input v-model="form.workExperience" placeholder="请输入工作经验（年）" />
        </el-form-item>
        <el-form-item label="当前薪资" prop="currentSalary">
          <el-input v-model="form.currentSalary" placeholder="请输入当前薪资" />
        </el-form-item>
        <el-form-item label="期望薪资" prop="expectedSalary">
          <el-input v-model="form.expectedSalary" placeholder="请输入期望薪资" />
        </el-form-item>
        <el-form-item label="VIP状态" prop="isVip">
          <el-radio-group v-model="form.isVip">
            <el-radio label="0">非VIP</el-radio>
            <el-radio label="1">VIP用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="VIP到期时间" prop="vipExpireTime" v-if="form.isVip == '1'">
          <el-date-picker clearable
            v-model="form.vipExpireTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择VIP到期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">冻结</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog title="用户详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户名">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="真实姓名">{{ detailForm.realName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detailForm.phonenumber }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ detailForm.email }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ detailForm.idCard }}</el-descriptions-item>
        <el-descriptions-item label="出生日期">{{ parseTime(detailForm.birthDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="学历">{{ detailForm.education }}</el-descriptions-item>
        <el-descriptions-item label="工作经验">{{ detailForm.workExperience }}年</el-descriptions-item>
        <el-descriptions-item label="当前薪资">{{ detailForm.currentSalary }}</el-descriptions-item>
        <el-descriptions-item label="期望薪资">{{ detailForm.expectedSalary }}</el-descriptions-item>
        <el-descriptions-item label="VIP状态">
          <dict-tag :options="dict.type.sys_yes_no" :value="detailForm.isVip"/>
        </el-descriptions-item>
        <el-descriptions-item label="VIP到期时间">{{ parseTime(detailForm.vipExpireTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="查看联系方式次数">{{ detailForm.viewContactCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.sys_normal_disable" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ detailForm.provinceName }} {{ detailForm.cityName }} {{ detailForm.districtName }} {{ detailForm.address }}</el-descriptions-item>
        <el-descriptions-item label="自我介绍" :span="2">{{ detailForm.selfIntro }}</el-descriptions-item>
        <el-descriptions-item label="技能标签" :span="2">{{ detailForm.skills }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listUserProfile, getUserProfile, delUserProfile, addUserProfile, updateUserProfile, freezeUser, unfreezeUser } from "@/api/recruitment/userProfile";

export default {
  name: "UserProfile",
  dicts: ['sys_yes_no', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户扩展表表格数据
      userProfileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        realName: null,
        isVip: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 详情表单参数
      detailForm: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户扩展表列表 */
    getList() {
      this.loading = true;
      listUserProfile(this.queryParams).then(response => {
        this.userProfileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        profileId: null,
        userId: null,
        realName: null,
        idCard: null,
        birthDate: null,
        education: null,
        workExperience: null,
        currentSalary: null,
        expectedSalary: null,
        provinceId: null,
        cityId: null,
        districtId: null,
        address: null,
        resumeUrl: null,
        selfIntro: null,
        skills: null,
        isVip: "0",
        vipExpireTime: null,
        viewContactCount: null,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.profileId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户扩展表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const profileId = row.profileId || this.ids
      getUserProfile(profileId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户扩展表";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const profileId = row.profileId;
      getUserProfile(profileId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 冻结按钮操作 */
    handleFreeze(row) {
      const userId = row.userId;
      this.$modal.confirm('是否确认冻结用户"' + row.realName + '"？').then(function() {
        return freezeUser(userId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("冻结成功");
      }).catch(() => {});
    },
    /** 解冻按钮操作 */
    handleUnfreeze(row) {
      const userId = row.userId;
      this.$modal.confirm('是否确认解冻用户"' + row.realName + '"？').then(function() {
        return unfreezeUser(userId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("解冻成功");
      }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.profileId != null) {
            updateUserProfile(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUserProfile(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const profileIds = row.profileId || this.ids;
      this.$modal.confirm('是否确认删除用户扩展表编号为"' + profileIds + '"的数据项？').then(function() {
        return delUserProfile(profileIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('recruitment/userProfile/export', {
        ...this.queryParams
      }, `userProfile_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
