<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="消费类型" prop="consumptionType">
        <el-select v-model="queryParams.consumptionType" placeholder="请选择消费类型" clearable>
          <el-option label="查看联系方式" value="1" />
          <el-option label="购买VIP" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['recruitment:consumptionRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['recruitment:consumptionRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="consumptionRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="recordId" />
      <el-table-column label="用户名" align="center" prop="userName" />
      <el-table-column label="用户昵称" align="center" prop="nickName" />
      <el-table-column label="手机号" align="center" prop="phonenumber" />
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column label="消费类型" align="center" prop="consumptionType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.consumptionType === '1'" type="primary">查看联系方式</el-tag>
          <el-tag v-else-if="scope.row.consumptionType === '2'" type="success">购买VIP</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="消费金额" align="center" prop="amount">
        <template slot-scope="scope">
          <span style="color: #f56c6c;">¥{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消费描述" align="center" prop="description" />
      <el-table-column label="消费时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['recruitment:consumptionRecord:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['recruitment:consumptionRecord:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 消费记录详情对话框 -->
    <el-dialog title="消费记录详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ detailForm.recordId }}</el-descriptions-item>
        <el-descriptions-item label="订单ID">{{ detailForm.orderId }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ detailForm.nickName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detailForm.phonenumber }}</el-descriptions-item>
        <el-descriptions-item label="订单号">{{ detailForm.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="消费类型">
          <el-tag v-if="detailForm.consumptionType === '1'" type="primary">查看联系方式</el-tag>
          <el-tag v-else-if="detailForm.consumptionType === '2'" type="success">购买VIP</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="消费金额">
          <span style="color: #f56c6c;">¥{{ detailForm.amount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="消费时间">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="消费描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 统计信息卡片 -->
    <el-row :gutter="20" class="mb20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总消费金额</span>
          </div>
          <div class="text item">
            <span style="font-size: 24px; color: #f56c6c;">¥{{ totalAmount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总消费次数</span>
          </div>
          <div class="text item">
            <span style="font-size: 24px; color: #409eff;">{{ totalCount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>查看联系方式消费</span>
          </div>
          <div class="text item">
            <span style="font-size: 20px; color: #67c23a;">¥{{ viewContactAmount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>VIP消费</span>
          </div>
          <div class="text item">
            <span style="font-size: 20px; color: #e6a23c;">¥{{ vipAmount }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listConsumptionRecord, getConsumptionRecord, delConsumptionRecord } from "@/api/recruitment/consumptionRecord";

export default {
  name: "ConsumptionRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消费记录表表格数据
      consumptionRecordList: [],
      // 是否显示详情弹出层
      detailOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        consumptionType: null,
        orderNo: null,
      },
      // 详情表单参数
      detailForm: {},
      // 统计数据
      totalAmount: 0,
      totalCount: 0,
      viewContactAmount: 0,
      vipAmount: 0
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询消费记录表列表 */
    getList() {
      this.loading = true;
      listConsumptionRecord(this.queryParams).then(response => {
        this.consumptionRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      // 这里可以调用统计接口获取数据
      // 暂时使用模拟数据
      this.totalAmount = 1250.00;
      this.totalCount = 85;
      this.viewContactAmount = 450.00;
      this.vipAmount = 800.00;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const recordId = row.recordId;
      getConsumptionRecord(recordId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.recordId || this.ids;
      this.$modal.confirm('是否确认删除消费记录编号为"' + recordIds + '"的数据项？').then(function() {
        return delConsumptionRecord(recordIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('recruitment/consumptionRecord/export', {
        ...this.queryParams
      }, `consumptionRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}

.box-card {
  width: 100%;
}
</style>
