<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="queryParams.orderType" placeholder="请选择订单类型" clearable>
          <el-option label="查看联系方式" value="1" />
          <el-option label="VIP会员" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
          <el-option label="待支付" value="0" />
          <el-option label="已支付" value="1" />
          <el-option label="已退款" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="退款状态" prop="refundStatus">
        <el-select v-model="queryParams.refundStatus" placeholder="请选择退款状态" clearable>
          <el-option label="无退款" value="0" />
          <el-option label="退款中" value="1" />
          <el-option label="已退款" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['recruitment:order:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-time"
          size="mini"
          @click="handleAutoCancelExpired"
          v-hasPermi="['recruitment:order:cancel']"
        >取消过期订单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
      <el-table-column label="用户名" align="center" prop="userName" />
      <el-table-column label="订单类型" align="center" prop="orderType">
        <template slot-scope="scope">
          <dict-tag :options="orderTypeOptions" :value="scope.row.orderType"/>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="订单金额" align="center" prop="amount" />
      <el-table-column label="支付状态" align="center" prop="payStatus">
        <template slot-scope="scope">
          <dict-tag :options="payStatusOptions" :value="scope.row.payStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="payTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.payTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payMethod" />
      <el-table-column label="退款状态" align="center" prop="refundStatus">
        <template slot-scope="scope">
          <dict-tag :options="refundStatusOptions" :value="scope.row.refundStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="退款金额" align="center" prop="refundAmount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['recruitment:order:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.refundStatus == '1'"
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handleProcessRefund(scope.row)"
            v-hasPermi="['recruitment:order:refund']"
          >处理退款</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ detailForm.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="订单类型">
          <dict-tag :options="orderTypeOptions" :value="detailForm.orderType"/>
        </el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ detailForm.productName }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">{{ detailForm.amount }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <dict-tag :options="payStatusOptions" :value="detailForm.payStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(detailForm.payTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ detailForm.payMethod }}</el-descriptions-item>
        <el-descriptions-item label="交易流水号">{{ detailForm.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="退款状态">
          <dict-tag :options="refundStatusOptions" :value="detailForm.refundStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="退款时间">{{ parseTime(detailForm.refundTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="退款金额">{{ detailForm.refundAmount }}</el-descriptions-item>
        <el-descriptions-item label="退款原因" :span="2">{{ detailForm.refundReason }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ parseTime(detailForm.expireTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理退款对话框 -->
    <el-dialog title="处理退款" :visible.sync="refundOpen" width="500px" append-to-body>
      <el-form ref="refundForm" :model="refundForm" :rules="refundRules" label-width="80px">
        <el-form-item label="订单号">
          <el-input v-model="refundForm.orderNo" :disabled="true" />
        </el-form-item>
        <el-form-item label="订单金额">
          <el-input v-model="refundForm.amount" :disabled="true" />
        </el-form-item>
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input v-model="refundForm.refundAmount" placeholder="请输入退款金额" />
        </el-form-item>
        <el-form-item label="退款原因">
          <el-input v-model="refundForm.refundReason" type="textarea" placeholder="请输入退款原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRefund">确 定</el-button>
        <el-button @click="cancelRefund">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, processRefund, autoCancelExpiredOrders } from "@/api/recruitment/order";

export default {
  name: "Order",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示退款弹出层
      refundOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        userName: null,
        orderType: null,
        payStatus: null,
        refundStatus: null,
      },
      // 详情表单参数
      detailForm: {},
      // 退款表单参数
      refundForm: {},
      // 退款表单校验
      refundRules: {
        refundAmount: [
          { required: true, message: "退款金额不能为空", trigger: "blur" }
        ],
      },
      // 订单类型选项
      orderTypeOptions: [
        { label: "查看联系方式", value: "1" },
        { label: "VIP会员", value: "2" }
      ],
      // 支付状态选项
      payStatusOptions: [
        { label: "待支付", value: "0" },
        { label: "已支付", value: "1" },
        { label: "已退款", value: "2" }
      ],
      // 退款状态选项
      refundStatusOptions: [
        { label: "无退款", value: "0" },
        { label: "退款中", value: "1" },
        { label: "已退款", value: "2" }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单表列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const orderId = row.orderId;
      getOrder(orderId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 处理退款按钮操作 */
    handleProcessRefund(row) {
      this.refundForm = {
        orderId: row.orderId,
        orderNo: row.orderNo,
        amount: row.amount,
        refundAmount: row.amount,
        refundReason: row.refundReason
      };
      this.refundOpen = true;
    },
    /** 提交退款 */
    submitRefund() {
      this.$refs["refundForm"].validate(valid => {
        if (valid) {
          processRefund(this.refundForm.orderId, this.refundForm).then(response => {
            this.$modal.msgSuccess("退款处理成功");
            this.refundOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消退款 */
    cancelRefund() {
      this.refundOpen = false;
      this.refundForm = {};
    },
    /** 自动取消过期订单 */
    handleAutoCancelExpired() {
      this.$modal.confirm('是否确认取消所有过期订单？').then(function() {
        return autoCancelExpiredOrders();
      }).then((response) => {
        this.getList();
        this.$modal.msgSuccess(response.msg);
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('recruitment/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
