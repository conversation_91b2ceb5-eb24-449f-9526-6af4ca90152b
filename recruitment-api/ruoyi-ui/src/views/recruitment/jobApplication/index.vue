<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="职位标题" prop="jobTitle">
        <el-input
          v-model="queryParams.jobTitle"
          placeholder="请输入职位标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择申请状态" clearable>
          <el-option label="待处理" value="0" />
          <el-option label="已查看" value="1" />
          <el-option label="已沟通" value="2" />
          <el-option label="已拒绝" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['recruitment:jobApplication:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['recruitment:jobApplication:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="jobApplicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="applicationId" />
      <el-table-column label="职位标题" align="center" prop="jobTitle" />
      <el-table-column label="申请人" align="center" prop="realName" />
      <el-table-column label="联系电话" align="center" prop="phonenumber" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="申请时间" align="center" prop="applyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" type="info">待处理</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="primary">已查看</el-tag>
          <el-tag v-else-if="scope.row.status === '2'" type="success">已沟通</el-tag>
          <el-tag v-else-if="scope.row.status === '3'" type="danger">已拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['recruitment:jobApplication:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleView(scope.row)"
            v-hasPermi="['recruitment:jobApplication:view']"
          >标记已查看</el-button>
          <el-button
            v-if="scope.row.status === '1' || scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-chat-dot-round"
            @click="handleCommunicate(scope.row)"
            v-hasPermi="['recruitment:jobApplication:communicate']"
          >沟通</el-button>
          <el-button
            v-if="scope.row.status !== '3'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleReject(scope.row)"
            v-hasPermi="['recruitment:jobApplication:reject']"
          >拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['recruitment:jobApplication:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 申请详情对话框 -->
    <el-dialog title="申请详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请ID">{{ detailForm.applicationId }}</el-descriptions-item>
        <el-descriptions-item label="职位标题">{{ detailForm.jobTitle }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ detailForm.realName }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.phonenumber }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ detailForm.companyName }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(detailForm.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag v-if="detailForm.status === '0'" type="info">待处理</el-tag>
          <el-tag v-else-if="detailForm.status === '1'" type="primary">已查看</el-tag>
          <el-tag v-else-if="detailForm.status === '2'" type="success">已沟通</el-tag>
          <el-tag v-else-if="detailForm.status === '3'" type="danger">已拒绝</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 沟通对话框 -->
    <el-dialog title="沟通申请者" :visible.sync="communicateOpen" width="500px" append-to-body>
      <el-form ref="communicateForm" :model="communicateForm" :rules="communicateRules" label-width="80px">
        <el-form-item label="申请人">
          <el-input v-model="communicateForm.realName" :disabled="true" />
        </el-form-item>
        <el-form-item label="职位标题">
          <el-input v-model="communicateForm.jobTitle" :disabled="true" />
        </el-form-item>
        <el-form-item label="沟通备注" prop="remark">
          <el-input v-model="communicateForm.remark" type="textarea" placeholder="请输入沟通内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCommunicate">确 定</el-button>
        <el-button @click="cancelCommunicate">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 拒绝对话框 -->
    <el-dialog title="拒绝申请" :visible.sync="rejectOpen" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="80px">
        <el-form-item label="申请人">
          <el-input v-model="rejectForm.realName" :disabled="true" />
        </el-form-item>
        <el-form-item label="职位标题">
          <el-input v-model="rejectForm.jobTitle" :disabled="true" />
        </el-form-item>
        <el-form-item label="拒绝原因" prop="remark">
          <el-input v-model="rejectForm.remark" type="textarea" placeholder="请输入拒绝原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReject">确 定</el-button>
        <el-button @click="cancelReject">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listJobApplication, getJobApplication, delJobApplication, viewApplication, communicateApplicant, rejectApplication } from "@/api/recruitment/jobApplication";

export default {
  name: "JobApplication",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 求职申请表表格数据
      jobApplicationList: [],
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示沟通弹出层
      communicateOpen: false,
      // 是否显示拒绝弹出层
      rejectOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobTitle: null,
        realName: null,
        companyName: null,
        status: null,
      },
      // 详情表单参数
      detailForm: {},
      // 沟通表单参数
      communicateForm: {},
      // 拒绝表单参数
      rejectForm: {},
      // 沟通表单校验
      communicateRules: {
        remark: [
          { required: true, message: "沟通备注不能为空", trigger: "blur" }
        ],
      },
      // 拒绝表单校验
      rejectRules: {
        remark: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询求职申请表列表 */
    getList() {
      this.loading = true;
      listJobApplication(this.queryParams).then(response => {
        this.jobApplicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const applicationId = row.applicationId;
      getJobApplication(applicationId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 标记已查看 */
    handleView(row) {
      const applicationId = row.applicationId;
      this.$modal.confirm('是否确认标记申请"' + row.realName + '"为已查看？').then(function() {
        return viewApplication(applicationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("操作成功");
      }).catch(() => {});
    },
    /** 沟通申请者 */
    handleCommunicate(row) {
      this.communicateForm = {
        applicationId: row.applicationId,
        realName: row.realName,
        jobTitle: row.jobTitle,
        remark: ''
      };
      this.communicateOpen = true;
    },
    /** 提交沟通 */
    submitCommunicate() {
      this.$refs["communicateForm"].validate(valid => {
        if (valid) {
          communicateApplicant(this.communicateForm.applicationId, this.communicateForm).then(response => {
            this.$modal.msgSuccess("沟通成功");
            this.communicateOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消沟通 */
    cancelCommunicate() {
      this.communicateOpen = false;
      this.communicateForm = {};
    },
    /** 拒绝申请 */
    handleReject(row) {
      this.rejectForm = {
        applicationId: row.applicationId,
        realName: row.realName,
        jobTitle: row.jobTitle,
        remark: ''
      };
      this.rejectOpen = true;
    },
    /** 提交拒绝 */
    submitReject() {
      this.$refs["rejectForm"].validate(valid => {
        if (valid) {
          rejectApplication(this.rejectForm.applicationId, this.rejectForm).then(response => {
            this.$modal.msgSuccess("拒绝成功");
            this.rejectOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消拒绝 */
    cancelReject() {
      this.rejectOpen = false;
      this.rejectForm = {};
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const applicationIds = row.applicationId || this.ids;
      this.$modal.confirm('是否确认删除求职申请编号为"' + applicationIds + '"的数据项？').then(function() {
        return delJobApplication(applicationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('recruitment/jobApplication/export', {
        ...this.queryParams
      }, `jobApplication_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
