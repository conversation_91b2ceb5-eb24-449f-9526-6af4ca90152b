-- 招聘平台菜单权限配置
-- 主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('招聘管理', 0, 5, 'recruitment', NULL, 1, 0, 'M', '0', '0', '', 'job', 'admin', sysdate(), '', NULL, '招聘平台管理菜单');

-- 获取刚插入的招聘管理菜单ID
SET @recruitment_menu_id = LAST_INSERT_ID();

-- 用户管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('用户管理', @recruitment_menu_id, 1, 'userProfile', 'recruitment/userProfile/index', 1, 0, 'C', '0', '0', 'recruitment:userProfile:list', 'user', 'admin', sysdate(), '', NULL, '用户扩展信息管理');

SET @user_profile_menu_id = LAST_INSERT_ID();

-- 用户管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('用户查询', @user_profile_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:query', '#', 'admin', sysdate(), '', NULL, ''),
('用户新增', @user_profile_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:add', '#', 'admin', sysdate(), '', NULL, ''),
('用户修改', @user_profile_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:edit', '#', 'admin', sysdate(), '', NULL, ''),
('用户删除', @user_profile_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:remove', '#', 'admin', sysdate(), '', NULL, ''),
('用户导出', @user_profile_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:export', '#', 'admin', sysdate(), '', NULL, ''),
('用户冻结', @user_profile_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:freeze', '#', 'admin', sysdate(), '', NULL, ''),
('用户解冻', @user_profile_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:unfreeze', '#', 'admin', sysdate(), '', NULL, ''),
('VIP管理', @user_profile_menu_id, 8, '', '', 1, 0, 'F', '0', '0', 'recruitment:userProfile:vip', '#', 'admin', sysdate(), '', NULL, '');

-- 商家管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('商家管理', @recruitment_menu_id, 2, 'merchant', 'recruitment/merchant/index', 1, 0, 'C', '0', '0', 'recruitment:merchant:list', 'peoples', 'admin', sysdate(), '', NULL, '商家信息管理');

SET @merchant_menu_id = LAST_INSERT_ID();

-- 商家管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('商家查询', @merchant_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:merchant:query', '#', 'admin', sysdate(), '', NULL, ''),
('商家新增', @merchant_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:merchant:add', '#', 'admin', sysdate(), '', NULL, ''),
('商家修改', @merchant_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:merchant:edit', '#', 'admin', sysdate(), '', NULL, ''),
('商家删除', @merchant_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:merchant:remove', '#', 'admin', sysdate(), '', NULL, ''),
('商家导出', @merchant_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:merchant:export', '#', 'admin', sysdate(), '', NULL, ''),
('商家审核', @merchant_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:merchant:audit', '#', 'admin', sysdate(), '', NULL, '');

-- 职位管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('职位管理', @recruitment_menu_id, 3, 'job', 'recruitment/job/index', 1, 0, 'C', '0', '0', 'recruitment:job:list', 'skill', 'admin', sysdate(), '', NULL, '职位信息管理');

SET @job_menu_id = LAST_INSERT_ID();

-- 职位管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('职位查询', @job_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:job:query', '#', 'admin', sysdate(), '', NULL, ''),
('职位新增', @job_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:job:add', '#', 'admin', sysdate(), '', NULL, ''),
('职位修改', @job_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:job:edit', '#', 'admin', sysdate(), '', NULL, ''),
('职位删除', @job_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:job:remove', '#', 'admin', sysdate(), '', NULL, ''),
('职位导出', @job_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:job:export', '#', 'admin', sysdate(), '', NULL, ''),
('职位审核', @job_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:job:audit', '#', 'admin', sysdate(), '', NULL, '');

-- 求职申请管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('求职申请', @recruitment_menu_id, 4, 'jobApplication', 'recruitment/jobApplication/index', 1, 0, 'C', '0', '0', 'recruitment:jobApplication:list', 'form', 'admin', sysdate(), '', NULL, '求职申请管理');

SET @job_application_menu_id = LAST_INSERT_ID();

-- 求职申请权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('申请查询', @job_application_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:query', '#', 'admin', sysdate(), '', NULL, ''),
('申请删除', @job_application_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:remove', '#', 'admin', sysdate(), '', NULL, ''),
('申请导出', @job_application_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:export', '#', 'admin', sysdate(), '', NULL, ''),
('查看申请', @job_application_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:view', '#', 'admin', sysdate(), '', NULL, ''),
('沟通申请', @job_application_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:communicate', '#', 'admin', sysdate(), '', NULL, ''),
('拒绝申请', @job_application_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:reject', '#', 'admin', sysdate(), '', NULL, ''),
('商家申请', @job_application_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:merchant', '#', 'admin', sysdate(), '', NULL, ''),
('职位申请', @job_application_menu_id, 8, '', '', 1, 0, 'F', '0', '0', 'recruitment:jobApplication:job', '#', 'admin', sysdate(), '', NULL, '');

-- 订单管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('订单管理', @recruitment_menu_id, 5, 'order', 'recruitment/order/index', 1, 0, 'C', '0', '0', 'recruitment:order:list', 'money', 'admin', sysdate(), '', NULL, '订单信息管理');

SET @order_menu_id = LAST_INSERT_ID();

-- 订单管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('订单查询', @order_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:query', '#', 'admin', sysdate(), '', NULL, ''),
('订单新增', @order_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:add', '#', 'admin', sysdate(), '', NULL, ''),
('订单修改', @order_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:edit', '#', 'admin', sysdate(), '', NULL, ''),
('订单删除', @order_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:remove', '#', 'admin', sysdate(), '', NULL, ''),
('订单导出', @order_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:export', '#', 'admin', sysdate(), '', NULL, ''),
('处理退款', @order_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:refund', '#', 'admin', sysdate(), '', NULL, ''),
('取消订单', @order_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'recruitment:order:cancel', '#', 'admin', sysdate(), '', NULL, '');

-- 消费记录管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('消费记录', @recruitment_menu_id, 6, 'consumptionRecord', 'recruitment/consumptionRecord/index', 1, 0, 'C', '0', '0', 'recruitment:consumptionRecord:list', 'shopping', 'admin', sysdate(), '', NULL, '消费记录管理');

SET @consumption_record_menu_id = LAST_INSERT_ID();

-- 消费记录权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('记录查询', @consumption_record_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:consumptionRecord:query', '#', 'admin', sysdate(), '', NULL, ''),
('记录新增', @consumption_record_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:consumptionRecord:add', '#', 'admin', sysdate(), '', NULL, ''),
('记录修改', @consumption_record_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:consumptionRecord:edit', '#', 'admin', sysdate(), '', NULL, ''),
('记录删除', @consumption_record_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:consumptionRecord:remove', '#', 'admin', sysdate(), '', NULL, ''),
('记录导出', @consumption_record_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:consumptionRecord:export', '#', 'admin', sysdate(), '', NULL, ''),
('用户记录', @consumption_record_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:consumptionRecord:user', '#', 'admin', sysdate(), '', NULL, '');

-- 系统配置管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('系统配置', @recruitment_menu_id, 7, 'systemConfig', 'recruitment/systemConfig/index', 1, 0, 'C', '0', '0', 'recruitment:systemConfig:list', 'system', 'admin', sysdate(), '', NULL, '招聘系统配置管理');

SET @system_config_menu_id = LAST_INSERT_ID();

-- 系统配置权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('配置查询', @system_config_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:query', '#', 'admin', sysdate(), '', NULL, ''),
('配置新增', @system_config_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:add', '#', 'admin', sysdate(), '', NULL, ''),
('配置修改', @system_config_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:edit', '#', 'admin', sysdate(), '', NULL, ''),
('配置删除', @system_config_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:remove', '#', 'admin', sysdate(), '', NULL, ''),
('配置导出', @system_config_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:export', '#', 'admin', sysdate(), '', NULL, ''),
('初始化配置', @system_config_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:init', '#', 'admin', sysdate(), '', NULL, ''),
('重置配置', @system_config_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:reset', '#', 'admin', sysdate(), '', NULL, ''),
('价格配置', @system_config_menu_id, 8, '', '', 1, 0, 'F', '0', '0', 'recruitment:systemConfig:price', '#', 'admin', sysdate(), '', NULL, '');

-- 地区管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('地区管理', @recruitment_menu_id, 8, 'region', 'recruitment/region/index', 1, 0, 'C', '0', '0', 'recruitment:region:list', 'tree', 'admin', sysdate(), '', NULL, '地区信息管理');

SET @region_menu_id = LAST_INSERT_ID();

-- 地区管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('地区查询', @region_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'recruitment:region:query', '#', 'admin', sysdate(), '', NULL, ''),
('地区新增', @region_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'recruitment:region:add', '#', 'admin', sysdate(), '', NULL, ''),
('地区修改', @region_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'recruitment:region:edit', '#', 'admin', sysdate(), '', NULL, ''),
('地区删除', @region_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'recruitment:region:remove', '#', 'admin', sysdate(), '', NULL, ''),
('地区导出', @region_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'recruitment:region:export', '#', 'admin', sysdate(), '', NULL, ''),
('同步地区', @region_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'recruitment:region:sync', '#', 'admin', sysdate(), '', NULL, '');
