package com.ruoyi.recruitment.service.impl;

import java.util.List;
import java.math.BigDecimal;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.recruitment.mapper.RecConsumptionRecordMapper;
import com.ruoyi.recruitment.domain.RecConsumptionRecord;
import com.ruoyi.recruitment.service.IRecConsumptionRecordService;

/**
 * 消费记录表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecConsumptionRecordServiceImpl implements IRecConsumptionRecordService 
{
    @Autowired
    private RecConsumptionRecordMapper recConsumptionRecordMapper;

    /**
     * 查询消费记录表
     * 
     * @param recordId 消费记录表主键
     * @return 消费记录表
     */
    @Override
    public RecConsumptionRecord selectRecConsumptionRecordByRecordId(Long recordId)
    {
        return recConsumptionRecordMapper.selectRecConsumptionRecordByRecordId(recordId);
    }

    /**
     * 查询消费记录表列表
     * 
     * @param recConsumptionRecord 消费记录表
     * @return 消费记录表
     */
    @Override
    public List<RecConsumptionRecord> selectRecConsumptionRecordList(RecConsumptionRecord recConsumptionRecord)
    {
        return recConsumptionRecordMapper.selectRecConsumptionRecordList(recConsumptionRecord);
    }

    /**
     * 根据用户ID查询消费记录列表
     * 
     * @param userId 用户ID
     * @return 消费记录列表
     */
    @Override
    public List<RecConsumptionRecord> selectRecConsumptionRecordByUserId(Long userId)
    {
        return recConsumptionRecordMapper.selectRecConsumptionRecordByUserId(userId);
    }

    /**
     * 新增消费记录表
     * 
     * @param recConsumptionRecord 消费记录表
     * @return 结果
     */
    @Override
    public int insertRecConsumptionRecord(RecConsumptionRecord recConsumptionRecord)
    {
        recConsumptionRecord.setCreateTime(new Date());
        return recConsumptionRecordMapper.insertRecConsumptionRecord(recConsumptionRecord);
    }

    /**
     * 修改消费记录表
     * 
     * @param recConsumptionRecord 消费记录表
     * @return 结果
     */
    @Override
    public int updateRecConsumptionRecord(RecConsumptionRecord recConsumptionRecord)
    {
        return recConsumptionRecordMapper.updateRecConsumptionRecord(recConsumptionRecord);
    }

    /**
     * 批量删除消费记录表
     * 
     * @param recordIds 需要删除的消费记录表主键
     * @return 结果
     */
    @Override
    public int deleteRecConsumptionRecordByRecordIds(Long[] recordIds)
    {
        return recConsumptionRecordMapper.deleteRecConsumptionRecordByRecordIds(recordIds);
    }

    /**
     * 删除消费记录表信息
     * 
     * @param recordId 消费记录表主键
     * @return 结果
     */
    @Override
    public int deleteRecConsumptionRecordByRecordId(Long recordId)
    {
        return recConsumptionRecordMapper.deleteRecConsumptionRecordByRecordId(recordId);
    }

    /**
     * 创建查看联系方式消费记录
     * 
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param amount 消费金额
     * @param description 消费描述
     * @return 结果
     */
    @Override
    public int createViewContactRecord(Long userId, Long orderId, BigDecimal amount, String description)
    {
        RecConsumptionRecord record = new RecConsumptionRecord();
        record.setUserId(userId);
        record.setOrderId(orderId);
        record.setConsumptionType("1"); // 查看联系方式
        record.setAmount(amount);
        record.setDescription(description);
        return insertRecConsumptionRecord(record);
    }

    /**
     * 创建VIP购买消费记录
     * 
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param amount 消费金额
     * @param description 消费描述
     * @return 结果
     */
    @Override
    public int createVipPurchaseRecord(Long userId, Long orderId, BigDecimal amount, String description)
    {
        RecConsumptionRecord record = new RecConsumptionRecord();
        record.setUserId(userId);
        record.setOrderId(orderId);
        record.setConsumptionType("2"); // 购买VIP
        record.setAmount(amount);
        record.setDescription(description);
        return insertRecConsumptionRecord(record);
    }

    /**
     * 统计用户消费总额
     * 
     * @param userId 用户ID
     * @return 消费总额
     */
    @Override
    public BigDecimal sumUserConsumption(Long userId)
    {
        BigDecimal amount = recConsumptionRecordMapper.sumUserConsumption(userId);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    /**
     * 统计用户某类型消费总额
     * 
     * @param userId 用户ID
     * @param consumptionType 消费类型
     * @return 消费总额
     */
    @Override
    public BigDecimal sumUserConsumptionByType(Long userId, String consumptionType)
    {
        BigDecimal amount = recConsumptionRecordMapper.sumUserConsumptionByType(userId, consumptionType);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    /**
     * 统计用户消费次数
     * 
     * @param userId 用户ID
     * @return 消费次数
     */
    @Override
    public int countUserConsumption(Long userId)
    {
        return recConsumptionRecordMapper.countUserConsumption(userId);
    }

    /**
     * 统计某类型消费次数
     * 
     * @param userId 用户ID
     * @param consumptionType 消费类型
     * @return 消费次数
     */
    @Override
    public int countUserConsumptionByType(Long userId, String consumptionType)
    {
        return recConsumptionRecordMapper.countUserConsumptionByType(userId, consumptionType);
    }

    /**
     * 查询最近消费记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 消费记录列表
     */
    @Override
    public List<RecConsumptionRecord> selectRecentConsumptionRecords(Long userId, int limit)
    {
        return recConsumptionRecordMapper.selectRecentConsumptionRecords(userId, limit);
    }
}
