package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecOrder;
import com.ruoyi.recruitment.service.IRecOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 订单表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/order")
public class RecOrderController extends BaseController
{
    @Autowired
    private IRecOrderService recOrderService;

    /**
     * 查询订单表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecOrder recOrder)
    {
        startPage();
        List<RecOrder> list = recOrderService.selectRecOrderList(recOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:export')")
    @Log(title = "订单表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecOrder recOrder)
    {
        List<RecOrder> list = recOrderService.selectRecOrderList(recOrder);
        ExcelUtil<RecOrder> util = new ExcelUtil<RecOrder>(RecOrder.class);
        util.exportExcel(response, list, "订单表数据");
    }

    /**
     * 获取订单表详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(recOrderService.selectRecOrderByOrderId(orderId));
    }

    /**
     * 根据订单号查询订单
     */
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getInfoByOrderNo(@PathVariable("orderNo") String orderNo)
    {
        RecOrder order = recOrderService.selectRecOrderByOrderNo(orderNo);
        // 只允许查看自己的订单或管理员查看
        Long userId = SecurityUtils.getUserId();
        if (order != null && !order.getUserId().equals(userId) && !SecurityUtils.isAdmin(userId)) {
            return error("无权限查看该订单");
        }
        return success(order);
    }

    /**
     * 新增订单表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:add')")
    @Log(title = "订单表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecOrder recOrder)
    {
        return toAjax(recOrderService.insertRecOrder(recOrder));
    }

    /**
     * 修改订单表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:edit')")
    @Log(title = "订单表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecOrder recOrder)
    {
        return toAjax(recOrderService.updateRecOrder(recOrder));
    }

    /**
     * 删除订单表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:remove')")
    @Log(title = "订单表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds)
    {
        return toAjax(recOrderService.deleteRecOrderByOrderIds(orderIds));
    }

    /**
     * 创建查看联系方式订单
     */
    @PostMapping("/createViewContact/{jobId}")
    public AjaxResult createViewContactOrder(@PathVariable Long jobId)
    {
        try {
            Long userId = SecurityUtils.getUserId();
            RecOrder order = recOrderService.createViewContactOrder(userId, jobId);
            return success(order);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 创建VIP会员订单
     */
    @PostMapping("/createVip/{vipType}")
    public AjaxResult createVipOrder(@PathVariable String vipType)
    {
        try {
            Long userId = SecurityUtils.getUserId();
            RecOrder order = recOrderService.createVipOrder(userId, vipType);
            return success(order);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 支付订单
     */
    @PostMapping("/pay/{orderNo}")
    public AjaxResult payOrder(@PathVariable String orderNo, @RequestBody RecOrder payInfo)
    {
        try {
            int result = recOrderService.payOrder(orderNo, payInfo.getPayMethod(), payInfo.getTransactionId());
            return result > 0 ? success("支付成功") : error("支付失败");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/cancel/{orderId}")
    public AjaxResult cancelOrder(@PathVariable Long orderId)
    {
        // 检查订单是否属于当前用户
        RecOrder order = recOrderService.selectRecOrderByOrderId(orderId);
        if (order == null) {
            return error("订单不存在");
        }
        
        Long userId = SecurityUtils.getUserId();
        if (!order.getUserId().equals(userId) && !SecurityUtils.isAdmin(userId)) {
            return error("无权限操作该订单");
        }
        
        return toAjax(recOrderService.cancelOrder(orderId));
    }

    /**
     * 申请退款
     */
    @PutMapping("/refund/{orderId}")
    public AjaxResult applyRefund(@PathVariable Long orderId, @RequestBody RecOrder refundInfo)
    {
        // 检查订单是否属于当前用户
        RecOrder order = recOrderService.selectRecOrderByOrderId(orderId);
        if (order == null) {
            return error("订单不存在");
        }
        
        Long userId = SecurityUtils.getUserId();
        if (!order.getUserId().equals(userId)) {
            return error("无权限操作该订单");
        }
        
        return toAjax(recOrderService.applyRefund(orderId, refundInfo.getRefundReason()));
    }

    /**
     * 处理退款（管理员）
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:refund')")
    @Log(title = "处理退款", businessType = BusinessType.UPDATE)
    @PutMapping("/processRefund/{orderId}")
    public AjaxResult processRefund(@PathVariable Long orderId, @RequestBody RecOrder refundInfo)
    {
        return toAjax(recOrderService.processRefund(orderId, refundInfo.getRefundAmount()));
    }

    /**
     * 获取当前用户的订单列表
     */
    @GetMapping("/my")
    public TableDataInfo getMyOrders()
    {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<RecOrder> list = recOrderService.selectRecOrderByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 查询待支付订单
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:list')")
    @GetMapping("/pending")
    public TableDataInfo getPendingPaymentOrders()
    {
        startPage();
        List<RecOrder> list = recOrderService.selectPendingPaymentOrders();
        return getDataTable(list);
    }

    /**
     * 查询已支付订单
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:list')")
    @GetMapping("/paid")
    public TableDataInfo getPaidOrders()
    {
        startPage();
        List<RecOrder> list = recOrderService.selectPaidOrders();
        return getDataTable(list);
    }

    /**
     * 查询退款订单
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:refund')")
    @GetMapping("/refund")
    public TableDataInfo getRefundOrders()
    {
        startPage();
        List<RecOrder> list = recOrderService.selectRefundOrders();
        return getDataTable(list);
    }

    /**
     * 统计用户订单数量
     */
    @GetMapping("/count/user/{userId}")
    public AjaxResult countUserOrders(@PathVariable Long userId)
    {
        int count = recOrderService.countUserOrders(userId);
        return success(count);
    }

    /**
     * 统计用户消费金额
     */
    @GetMapping("/amount/user/{userId}")
    public AjaxResult sumUserOrderAmount(@PathVariable Long userId)
    {
        java.math.BigDecimal amount = recOrderService.sumUserOrderAmount(userId);
        return success(amount);
    }

    /**
     * 统计当前用户订单数量
     */
    @GetMapping("/count/my")
    public AjaxResult countMyOrders()
    {
        Long userId = SecurityUtils.getUserId();
        int count = recOrderService.countUserOrders(userId);
        return success(count);
    }

    /**
     * 统计当前用户消费金额
     */
    @GetMapping("/amount/my")
    public AjaxResult sumMyOrderAmount()
    {
        Long userId = SecurityUtils.getUserId();
        java.math.BigDecimal amount = recOrderService.sumUserOrderAmount(userId);
        return success(amount);
    }

    /**
     * 自动取消过期订单
     */
    @PreAuthorize("@ss.hasPermi('recruitment:order:cancel')")
    @Log(title = "取消过期订单", businessType = BusinessType.UPDATE)
    @PostMapping("/autoCancelExpired")
    public AjaxResult autoCancelExpiredOrders()
    {
        int count = recOrderService.autoCancelExpiredOrders();
        return success("已取消 " + count + " 个过期订单");
    }
}
