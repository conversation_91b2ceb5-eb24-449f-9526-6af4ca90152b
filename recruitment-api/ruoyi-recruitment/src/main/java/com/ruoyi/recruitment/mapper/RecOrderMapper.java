package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecOrder;

/**
 * 订单表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RecOrderMapper 
{
    /**
     * 查询订单表
     * 
     * @param orderId 订单表主键
     * @return 订单表
     */
    public RecOrder selectRecOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单表
     */
    public RecOrder selectRecOrderByOrderNo(String orderNo);

    /**
     * 查询订单表列表
     * 
     * @param recOrder 订单表
     * @return 订单表集合
     */
    public List<RecOrder> selectRecOrderList(RecOrder recOrder);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单列表
     */
    public List<RecOrder> selectRecOrderByUserId(Long userId);

    /**
     * 新增订单表
     * 
     * @param recOrder 订单表
     * @return 结果
     */
    public int insertRecOrder(RecOrder recOrder);

    /**
     * 修改订单表
     * 
     * @param recOrder 订单表
     * @return 结果
     */
    public int updateRecOrder(RecOrder recOrder);

    /**
     * 删除订单表
     * 
     * @param orderId 订单表主键
     * @return 结果
     */
    public int deleteRecOrderByOrderId(Long orderId);

    /**
     * 批量删除订单表
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecOrderByOrderIds(Long[] orderIds);

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @param payTime 支付时间
     * @param transactionId 交易流水号
     * @return 结果
     */
    public int updateOrderPayStatus(String orderNo, String payStatus, java.util.Date payTime, String transactionId);

    /**
     * 更新订单退款状态
     * 
     * @param orderId 订单ID
     * @param refundStatus 退款状态
     * @param refundTime 退款时间
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 结果
     */
    public int updateOrderRefundStatus(Long orderId, String refundStatus, java.util.Date refundTime, 
                                     java.math.BigDecimal refundAmount, String refundReason);

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @return 订单数量
     */
    public int countUserOrders(Long userId);

    /**
     * 统计用户消费金额
     * 
     * @param userId 用户ID
     * @return 消费金额
     */
    public java.math.BigDecimal sumUserOrderAmount(Long userId);

    /**
     * 查询待支付订单
     * 
     * @return 待支付订单列表
     */
    public List<RecOrder> selectPendingPaymentOrders();

    /**
     * 查询已支付订单
     * 
     * @return 已支付订单列表
     */
    public List<RecOrder> selectPaidOrders();

    /**
     * 查询退款订单
     * 
     * @return 退款订单列表
     */
    public List<RecOrder> selectRefundOrders();

    /**
     * 查询过期订单
     * 
     * @return 过期订单列表
     */
    public List<RecOrder> selectExpiredOrders();
}
