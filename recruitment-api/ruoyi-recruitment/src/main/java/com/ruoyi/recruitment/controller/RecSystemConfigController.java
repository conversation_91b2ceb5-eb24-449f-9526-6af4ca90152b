package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecSystemConfig;
import com.ruoyi.recruitment.service.IRecSystemConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 系统配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/systemConfig")
public class RecSystemConfigController extends BaseController
{
    @Autowired
    private IRecSystemConfigService recSystemConfigService;

    /**
     * 查询系统配置列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecSystemConfig recSystemConfig)
    {
        startPage();
        List<RecSystemConfig> list = recSystemConfigService.selectRecSystemConfigList(recSystemConfig);
        return getDataTable(list);
    }

    /**
     * 导出系统配置列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:export')")
    @Log(title = "系统配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecSystemConfig recSystemConfig)
    {
        List<RecSystemConfig> list = recSystemConfigService.selectRecSystemConfigList(recSystemConfig);
        ExcelUtil<RecSystemConfig> util = new ExcelUtil<RecSystemConfig>(RecSystemConfig.class);
        util.exportExcel(response, list, "系统配置数据");
    }

    /**
     * 获取系统配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return success(recSystemConfigService.selectRecSystemConfigByConfigId(configId));
    }

    /**
     * 新增系统配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:add')")
    @Log(title = "系统配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecSystemConfig recSystemConfig)
    {
        return toAjax(recSystemConfigService.insertRecSystemConfig(recSystemConfig));
    }

    /**
     * 修改系统配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:edit')")
    @Log(title = "系统配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecSystemConfig recSystemConfig)
    {
        return toAjax(recSystemConfigService.updateRecSystemConfig(recSystemConfig));
    }

    /**
     * 删除系统配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:remove')")
    @Log(title = "系统配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(recSystemConfigService.deleteRecSystemConfigByConfigIds(configIds));
    }

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/key/{configKey}")
    public AjaxResult getConfigByKey(@PathVariable("configKey") String configKey)
    {
        String configValue = recSystemConfigService.selectConfigByKey(configKey);
        return success(configValue);
    }

    /**
     * 批量更新配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:edit')")
    @Log(title = "批量更新配置", businessType = BusinessType.UPDATE)
    @PutMapping("/batch")
    public AjaxResult batchUpdate(@RequestBody List<RecSystemConfig> configs)
    {
        int result = 0;
        for (RecSystemConfig config : configs) {
            result += recSystemConfigService.updateRecSystemConfig(config);
        }
        return result > 0 ? success("更新成功") : error("更新失败");
    }

    /**
     * 获取价格配置
     */
    @GetMapping("/price")
    public AjaxResult getPriceConfig()
    {
        RecSystemConfig viewContactPrice = recSystemConfigService.selectRecSystemConfigByKey("view_contact_price");
        RecSystemConfig vipMonthPrice = recSystemConfigService.selectRecSystemConfigByKey("vip_month_price");
        RecSystemConfig vipYearPrice = recSystemConfigService.selectRecSystemConfigByKey("vip_year_price");
        
        return success()
            .put("viewContactPrice", viewContactPrice != null ? viewContactPrice.getConfigValue() : "5.00")
            .put("vipMonthPrice", vipMonthPrice != null ? vipMonthPrice.getConfigValue() : "29.00")
            .put("vipYearPrice", vipYearPrice != null ? vipYearPrice.getConfigValue() : "299.00");
    }

    /**
     * 更新价格配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:price')")
    @Log(title = "更新价格配置", businessType = BusinessType.UPDATE)
    @PutMapping("/price")
    public AjaxResult updatePriceConfig(@RequestBody RecSystemConfig priceConfig)
    {
        // 这里可以接收一个包含多个价格配置的对象
        // 为简化，这里只演示单个配置的更新
        return toAjax(recSystemConfigService.updateRecSystemConfig(priceConfig));
    }

    /**
     * 初始化默认配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:init')")
    @Log(title = "初始化配置", businessType = BusinessType.INSERT)
    @PostMapping("/init")
    public AjaxResult initDefaultConfig()
    {
        int result = recSystemConfigService.initDefaultConfig();
        return result > 0 ? success("初始化成功，共创建 " + result + " 个配置项") : error("初始化失败");
    }

    /**
     * 重置配置为默认值
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:reset')")
    @Log(title = "重置配置", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{configKey}")
    public AjaxResult resetConfig(@PathVariable String configKey)
    {
        boolean result = recSystemConfigService.resetConfigToDefault(configKey);
        return result ? success("重置成功") : error("重置失败");
    }

    /**
     * 获取所有配置分组
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:list')")
    @GetMapping("/groups")
    public AjaxResult getConfigGroups()
    {
        List<String> groups = recSystemConfigService.selectConfigGroups();
        return success(groups);
    }

    /**
     * 根据分组获取配置列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:systemConfig:list')")
    @GetMapping("/group/{groupName}")
    public AjaxResult getConfigsByGroup(@PathVariable String groupName)
    {
        List<RecSystemConfig> configs = recSystemConfigService.selectConfigsByGroup(groupName);
        return success(configs);
    }
}
