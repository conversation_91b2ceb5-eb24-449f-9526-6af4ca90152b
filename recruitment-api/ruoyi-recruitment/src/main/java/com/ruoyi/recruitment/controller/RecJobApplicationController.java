package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecJobApplication;
import com.ruoyi.recruitment.service.IRecJobApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 求职申请表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/jobApplication")
public class RecJobApplicationController extends BaseController
{
    @Autowired
    private IRecJobApplicationService recJobApplicationService;

    /**
     * 查询求职申请表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecJobApplication recJobApplication)
    {
        startPage();
        List<RecJobApplication> list = recJobApplicationService.selectRecJobApplicationList(recJobApplication);
        return getDataTable(list);
    }

    /**
     * 导出求职申请表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:export')")
    @Log(title = "求职申请表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecJobApplication recJobApplication)
    {
        List<RecJobApplication> list = recJobApplicationService.selectRecJobApplicationList(recJobApplication);
        ExcelUtil<RecJobApplication> util = new ExcelUtil<RecJobApplication>(RecJobApplication.class);
        util.exportExcel(response, list, "求职申请表数据");
    }

    /**
     * 获取求职申请表详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(recJobApplicationService.selectRecJobApplicationByApplicationId(applicationId));
    }

    /**
     * 新增求职申请表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:add')")
    @Log(title = "求职申请表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecJobApplication recJobApplication)
    {
        return toAjax(recJobApplicationService.insertRecJobApplication(recJobApplication));
    }

    /**
     * 修改求职申请表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:edit')")
    @Log(title = "求职申请表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecJobApplication recJobApplication)
    {
        return toAjax(recJobApplicationService.updateRecJobApplication(recJobApplication));
    }

    /**
     * 删除求职申请表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:remove')")
    @Log(title = "求职申请表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(recJobApplicationService.deleteRecJobApplicationByApplicationIds(applicationIds));
    }

    /**
     * 用户申请职位
     */
    @PostMapping("/apply/{jobId}")
    public AjaxResult applyJob(@PathVariable Long jobId)
    {
        try {
            Long userId = SecurityUtils.getUserId();
            int result = recJobApplicationService.applyJob(userId, jobId);
            return result > 0 ? success("申请成功") : error("申请失败");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 检查用户是否已申请该职位
     */
    @GetMapping("/check/{jobId}")
    public AjaxResult checkApplication(@PathVariable Long jobId)
    {
        Long userId = SecurityUtils.getUserId();
        boolean hasApplied = recJobApplicationService.hasUserAppliedJob(userId, jobId);
        return success(hasApplied);
    }

    /**
     * 获取当前用户的申请列表
     */
    @GetMapping("/my")
    public TableDataInfo getMyApplications()
    {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<RecJobApplication> list = recJobApplicationService.selectRecJobApplicationByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 根据商家ID查询申请列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:merchant')")
    @GetMapping("/merchant/{merchantId}")
    public TableDataInfo getApplicationsByMerchant(@PathVariable Long merchantId)
    {
        startPage();
        List<RecJobApplication> list = recJobApplicationService.selectRecJobApplicationByMerchantId(merchantId);
        return getDataTable(list);
    }

    /**
     * 根据职位ID查询申请列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:job')")
    @GetMapping("/job/{jobId}")
    public TableDataInfo getApplicationsByJob(@PathVariable Long jobId)
    {
        startPage();
        List<RecJobApplication> list = recJobApplicationService.selectRecJobApplicationByJobId(jobId);
        return getDataTable(list);
    }

    /**
     * 商家查看申请
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:view')")
    @Log(title = "查看申请", businessType = BusinessType.UPDATE)
    @PutMapping("/view/{applicationId}")
    public AjaxResult viewApplication(@PathVariable Long applicationId)
    {
        return toAjax(recJobApplicationService.merchantViewApplication(applicationId));
    }

    /**
     * 商家沟通申请者
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:communicate')")
    @Log(title = "沟通申请者", businessType = BusinessType.UPDATE)
    @PutMapping("/communicate/{applicationId}")
    public AjaxResult communicateApplicant(@PathVariable Long applicationId, @RequestBody RecJobApplication recJobApplication)
    {
        return toAjax(recJobApplicationService.merchantCommunicateApplicant(applicationId, recJobApplication.getRemark()));
    }

    /**
     * 商家拒绝申请
     */
    @PreAuthorize("@ss.hasPermi('recruitment:jobApplication:reject')")
    @Log(title = "拒绝申请", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{applicationId}")
    public AjaxResult rejectApplication(@PathVariable Long applicationId, @RequestBody RecJobApplication recJobApplication)
    {
        return toAjax(recJobApplicationService.merchantRejectApplication(applicationId, recJobApplication.getRemark()));
    }

    /**
     * 统计用户申请次数
     */
    @GetMapping("/count/user/{userId}")
    public AjaxResult countUserApplications(@PathVariable Long userId)
    {
        int count = recJobApplicationService.countUserApplications(userId);
        return success(count);
    }

    /**
     * 统计职位申请次数
     */
    @GetMapping("/count/job/{jobId}")
    public AjaxResult countJobApplications(@PathVariable Long jobId)
    {
        int count = recJobApplicationService.countJobApplications(jobId);
        return success(count);
    }

    /**
     * 统计商家收到的申请次数
     */
    @GetMapping("/count/merchant/{merchantId}")
    public AjaxResult countMerchantApplications(@PathVariable Long merchantId)
    {
        int count = recJobApplicationService.countMerchantApplications(merchantId);
        return success(count);
    }

    /**
     * 统计当前用户申请次数
     */
    @GetMapping("/count/my")
    public AjaxResult countMyApplications()
    {
        Long userId = SecurityUtils.getUserId();
        int count = recJobApplicationService.countUserApplications(userId);
        return success(count);
    }
}
