package com.ruoyi.recruitment.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.recruitment.mapper.RecJobApplicationMapper;
import com.ruoyi.recruitment.mapper.RecJobMapper;
import com.ruoyi.recruitment.domain.RecJobApplication;
import com.ruoyi.recruitment.domain.RecJob;
import com.ruoyi.recruitment.service.IRecJobApplicationService;

/**
 * 求职申请表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecJobApplicationServiceImpl implements IRecJobApplicationService 
{
    @Autowired
    private RecJobApplicationMapper recJobApplicationMapper;
    
    @Autowired
    private RecJobMapper recJobMapper;

    /**
     * 查询求职申请表
     * 
     * @param applicationId 求职申请表主键
     * @return 求职申请表
     */
    @Override
    public RecJobApplication selectRecJobApplicationByApplicationId(Long applicationId)
    {
        return recJobApplicationMapper.selectRecJobApplicationByApplicationId(applicationId);
    }

    /**
     * 查询求职申请表列表
     * 
     * @param recJobApplication 求职申请表
     * @return 求职申请表
     */
    @Override
    public List<RecJobApplication> selectRecJobApplicationList(RecJobApplication recJobApplication)
    {
        return recJobApplicationMapper.selectRecJobApplicationList(recJobApplication);
    }

    /**
     * 根据用户ID查询申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    @Override
    public List<RecJobApplication> selectRecJobApplicationByUserId(Long userId)
    {
        return recJobApplicationMapper.selectRecJobApplicationByUserId(userId);
    }

    /**
     * 根据商家ID查询申请列表
     * 
     * @param merchantId 商家ID
     * @return 申请列表
     */
    @Override
    public List<RecJobApplication> selectRecJobApplicationByMerchantId(Long merchantId)
    {
        return recJobApplicationMapper.selectRecJobApplicationByMerchantId(merchantId);
    }

    /**
     * 根据职位ID查询申请列表
     * 
     * @param jobId 职位ID
     * @return 申请列表
     */
    @Override
    public List<RecJobApplication> selectRecJobApplicationByJobId(Long jobId)
    {
        return recJobApplicationMapper.selectRecJobApplicationByJobId(jobId);
    }

    /**
     * 新增求职申请表
     * 
     * @param recJobApplication 求职申请表
     * @return 结果
     */
    @Override
    public int insertRecJobApplication(RecJobApplication recJobApplication)
    {
        recJobApplication.setCreateTime(DateUtils.getNowDate());
        return recJobApplicationMapper.insertRecJobApplication(recJobApplication);
    }

    /**
     * 修改求职申请表
     * 
     * @param recJobApplication 求职申请表
     * @return 结果
     */
    @Override
    public int updateRecJobApplication(RecJobApplication recJobApplication)
    {
        recJobApplication.setUpdateTime(DateUtils.getNowDate());
        return recJobApplicationMapper.updateRecJobApplication(recJobApplication);
    }

    /**
     * 批量删除求职申请表
     * 
     * @param applicationIds 需要删除的求职申请表主键
     * @return 结果
     */
    @Override
    public int deleteRecJobApplicationByApplicationIds(Long[] applicationIds)
    {
        return recJobApplicationMapper.deleteRecJobApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除求职申请表信息
     * 
     * @param applicationId 求职申请表主键
     * @return 结果
     */
    @Override
    public int deleteRecJobApplicationByApplicationId(Long applicationId)
    {
        return recJobApplicationMapper.deleteRecJobApplicationByApplicationId(applicationId);
    }

    /**
     * 用户申请职位
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 结果
     */
    @Override
    public int applyJob(Long userId, Long jobId)
    {
        // 检查是否已申请
        if (hasUserAppliedJob(userId, jobId)) {
            throw new RuntimeException("您已申请过该职位");
        }
        
        // 获取职位信息
        RecJob job = recJobMapper.selectRecJobByJobId(jobId);
        if (job == null) {
            throw new RuntimeException("职位不存在");
        }
        
        if (!"2".equals(job.getPublishStatus())) {
            throw new RuntimeException("职位未发布，无法申请");
        }
        
        // 创建申请记录
        RecJobApplication application = new RecJobApplication();
        application.setJobId(jobId);
        application.setUserId(userId);
        application.setMerchantId(job.getMerchantId());
        application.setApplyTime(new Date());
        application.setStatus("0"); // 待处理
        
        int result = insertRecJobApplication(application);
        
        // 更新职位申请次数
        if (result > 0) {
            recJobMapper.increaseApplyCount(jobId);
        }
        
        return result;
    }

    /**
     * 检查用户是否已申请该职位
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 是否已申请
     */
    @Override
    public boolean hasUserAppliedJob(Long userId, Long jobId)
    {
        RecJobApplication application = recJobApplicationMapper.checkUserJobApplication(userId, jobId);
        return application != null;
    }

    /**
     * 更新申请状态
     * 
     * @param applicationId 申请ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateApplicationStatus(Long applicationId, String status)
    {
        return recJobApplicationMapper.updateApplicationStatus(applicationId, status);
    }

    /**
     * 商家查看申请
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    @Override
    public int merchantViewApplication(Long applicationId)
    {
        return updateApplicationStatus(applicationId, "1"); // 已查看
    }

    /**
     * 商家沟通申请者
     * 
     * @param applicationId 申请ID
     * @param remark 备注
     * @return 结果
     */
    @Override
    public int merchantCommunicateApplicant(Long applicationId, String remark)
    {
        RecJobApplication application = new RecJobApplication();
        application.setApplicationId(applicationId);
        application.setStatus("2"); // 已沟通
        application.setRemark(remark);
        return updateRecJobApplication(application);
    }

    /**
     * 商家拒绝申请
     * 
     * @param applicationId 申请ID
     * @param remark 拒绝原因
     * @return 结果
     */
    @Override
    public int merchantRejectApplication(Long applicationId, String remark)
    {
        RecJobApplication application = new RecJobApplication();
        application.setApplicationId(applicationId);
        application.setStatus("3"); // 已拒绝
        application.setRemark(remark);
        return updateRecJobApplication(application);
    }

    /**
     * 统计用户申请次数
     * 
     * @param userId 用户ID
     * @return 申请次数
     */
    @Override
    public int countUserApplications(Long userId)
    {
        return recJobApplicationMapper.countUserApplications(userId);
    }

    /**
     * 统计职位申请次数
     * 
     * @param jobId 职位ID
     * @return 申请次数
     */
    @Override
    public int countJobApplications(Long jobId)
    {
        return recJobApplicationMapper.countJobApplications(jobId);
    }

    /**
     * 统计商家收到的申请次数
     * 
     * @param merchantId 商家ID
     * @return 申请次数
     */
    @Override
    public int countMerchantApplications(Long merchantId)
    {
        return recJobApplicationMapper.countMerchantApplications(merchantId);
    }
}
