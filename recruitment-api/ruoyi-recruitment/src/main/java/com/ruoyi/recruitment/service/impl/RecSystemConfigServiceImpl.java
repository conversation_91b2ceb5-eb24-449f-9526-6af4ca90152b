package com.ruoyi.recruitment.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.mapper.RecSystemConfigMapper;
import com.ruoyi.recruitment.domain.RecSystemConfig;
import com.ruoyi.recruitment.service.IRecSystemConfigService;

/**
 * 系统配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecSystemConfigServiceImpl implements IRecSystemConfigService 
{
    @Autowired
    private RecSystemConfigMapper recSystemConfigMapper;

    /**
     * 查询系统配置
     * 
     * @param configId 系统配置主键
     * @return 系统配置
     */
    @Override
    public RecSystemConfig selectRecSystemConfigByConfigId(Long configId)
    {
        return recSystemConfigMapper.selectRecSystemConfigByConfigId(configId);
    }

    /**
     * 查询系统配置列表
     * 
     * @param recSystemConfig 系统配置
     * @return 系统配置
     */
    @Override
    public List<RecSystemConfig> selectRecSystemConfigList(RecSystemConfig recSystemConfig)
    {
        return recSystemConfigMapper.selectRecSystemConfigList(recSystemConfig);
    }

    /**
     * 新增系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    @Override
    public int insertRecSystemConfig(RecSystemConfig recSystemConfig)
    {
        recSystemConfig.setCreateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.insertRecSystemConfig(recSystemConfig);
    }

    /**
     * 修改系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    @Override
    public int updateRecSystemConfig(RecSystemConfig recSystemConfig)
    {
        recSystemConfig.setUpdateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.updateRecSystemConfig(recSystemConfig);
    }

    /**
     * 批量删除系统配置
     * 
     * @param configIds 需要删除的系统配置主键
     * @return 结果
     */
    @Override
    public int deleteRecSystemConfigByConfigIds(Long[] configIds)
    {
        return recSystemConfigMapper.deleteRecSystemConfigByConfigIds(configIds);
    }

    /**
     * 删除系统配置信息
     * 
     * @param configId 系统配置主键
     * @return 结果
     */
    @Override
    public int deleteRecSystemConfigByConfigId(Long configId)
    {
        return recSystemConfigMapper.deleteRecSystemConfigByConfigId(configId);
    }

    /**
     * 根据配置键查询配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    public String selectConfigByKey(String configKey)
    {
        RecSystemConfig config = recSystemConfigMapper.selectRecSystemConfigByKey(configKey);
        return config != null ? config.getConfigValue() : null;
    }

    /**
     * 根据配置键查询配置信息
     * 
     * @param configKey 配置键
     * @return 系统配置
     */
    @Override
    public RecSystemConfig selectRecSystemConfigByKey(String configKey)
    {
        return recSystemConfigMapper.selectRecSystemConfigByKey(configKey);
    }

    /**
     * 更新配置值
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 结果
     */
    @Override
    public int updateConfigByKey(String configKey, String configValue)
    {
        RecSystemConfig config = new RecSystemConfig();
        config.setConfigKey(configKey);
        config.setConfigValue(configValue);
        config.setUpdateBy(SecurityUtils.getUsername());
        config.setUpdateTime(DateUtils.getNowDate());

        return recSystemConfigMapper.updateConfigByKey(config);
    }

    /**
     * 初始化默认配置
     *
     * @return 创建的配置数量
     */
    @Override
    public int initDefaultConfig()
    {
        // 定义默认配置项
        String[][] defaultConfigs = {
            {"view_contact_price", "5.00", "查看联系方式价格", "price", "查看联系方式所需支付的费用"},
            {"vip_month_price", "29.00", "VIP月费价格", "price", "VIP会员月费价格"},
            {"vip_year_price", "299.00", "VIP年费价格", "price", "VIP会员年费价格"},
            {"max_job_publish", "10", "普通用户最大发布职位数", "limit", "普通用户最多可发布的职位数量"},
            {"vip_max_job_publish", "100", "VIP用户最大发布职位数", "limit", "VIP用户最多可发布的职位数量"},
            {"job_auto_offline_days", "30", "职位自动下线天数", "system", "职位发布后多少天自动下线"},
            {"user_daily_apply_limit", "20", "用户每日申请限制", "limit", "普通用户每日最多申请职位数"},
            {"vip_daily_apply_limit", "100", "VIP用户每日申请限制", "limit", "VIP用户每日最多申请职位数"},
            {"merchant_audit_auto", "0", "商家审核自动通过", "system", "新商家注册是否自动通过审核（0否1是）"},
            {"job_audit_auto", "0", "职位审核自动通过", "system", "职位发布是否自动通过审核（0否1是）"}
        };

        int count = 0;
        for (String[] config : defaultConfigs) {
            RecSystemConfig existConfig = recSystemConfigMapper.selectRecSystemConfigByKey(config[0]);
            if (existConfig == null) {
                RecSystemConfig newConfig = new RecSystemConfig();
                newConfig.setConfigKey(config[0]);
                newConfig.setConfigValue(config[1]);
                newConfig.setConfigName(config[2]);
                newConfig.setConfigType(config[3]);
                newConfig.setRemark(config[4]);
                newConfig.setCreateBy("system");
                newConfig.setCreateTime(DateUtils.getNowDate());

                if (insertRecSystemConfig(newConfig) > 0) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 重置配置为默认值
     *
     * @param configKey 配置键
     * @return 是否成功
     */
    @Override
    public boolean resetConfigToDefault(String configKey)
    {
        // 这里可以定义默认值映射
        String defaultValue = getDefaultValue(configKey);
        if (defaultValue != null) {
            return updateConfigByKey(configKey, defaultValue) > 0;
        }
        return false;
    }

    /**
     * 获取配置的默认值
     */
    private String getDefaultValue(String configKey) {
        switch (configKey) {
            case "view_contact_price": return "5.00";
            case "vip_month_price": return "29.00";
            case "vip_year_price": return "299.00";
            case "max_job_publish": return "10";
            case "vip_max_job_publish": return "100";
            case "job_auto_offline_days": return "30";
            case "user_daily_apply_limit": return "20";
            case "vip_daily_apply_limit": return "100";
            case "merchant_audit_auto": return "0";
            case "job_audit_auto": return "0";
            default: return null;
        }
    }

    /**
     * 获取所有配置分组
     *
     * @return 分组列表
     */
    @Override
    public List<String> selectConfigGroups()
    {
        return recSystemConfigMapper.selectConfigGroups();
    }

    /**
     * 根据分组获取配置列表
     *
     * @param groupName 分组名称
     * @return 配置列表
     */
    @Override
    public List<RecSystemConfig> selectConfigsByGroup(String groupName)
    {
        return recSystemConfigMapper.selectConfigsByGroup(groupName);
    }
}
