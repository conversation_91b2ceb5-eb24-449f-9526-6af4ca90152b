package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecConsumptionRecord;

/**
 * 消费记录表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecConsumptionRecordService 
{
    /**
     * 查询消费记录表
     * 
     * @param recordId 消费记录表主键
     * @return 消费记录表
     */
    public RecConsumptionRecord selectRecConsumptionRecordByRecordId(Long recordId);

    /**
     * 查询消费记录表列表
     * 
     * @param recConsumptionRecord 消费记录表
     * @return 消费记录表集合
     */
    public List<RecConsumptionRecord> selectRecConsumptionRecordList(RecConsumptionRecord recConsumptionRecord);

    /**
     * 根据用户ID查询消费记录列表
     * 
     * @param userId 用户ID
     * @return 消费记录列表
     */
    public List<RecConsumptionRecord> selectRecConsumptionRecordByUserId(Long userId);

    /**
     * 新增消费记录表
     * 
     * @param recConsumptionRecord 消费记录表
     * @return 结果
     */
    public int insertRecConsumptionRecord(RecConsumptionRecord recConsumptionRecord);

    /**
     * 修改消费记录表
     * 
     * @param recConsumptionRecord 消费记录表
     * @return 结果
     */
    public int updateRecConsumptionRecord(RecConsumptionRecord recConsumptionRecord);

    /**
     * 批量删除消费记录表
     * 
     * @param recordIds 需要删除的消费记录表主键集合
     * @return 结果
     */
    public int deleteRecConsumptionRecordByRecordIds(Long[] recordIds);

    /**
     * 删除消费记录表信息
     * 
     * @param recordId 消费记录表主键
     * @return 结果
     */
    public int deleteRecConsumptionRecordByRecordId(Long recordId);

    /**
     * 创建查看联系方式消费记录
     * 
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param amount 消费金额
     * @param description 消费描述
     * @return 结果
     */
    public int createViewContactRecord(Long userId, Long orderId, java.math.BigDecimal amount, String description);

    /**
     * 创建VIP购买消费记录
     * 
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param amount 消费金额
     * @param description 消费描述
     * @return 结果
     */
    public int createVipPurchaseRecord(Long userId, Long orderId, java.math.BigDecimal amount, String description);

    /**
     * 统计用户消费总额
     * 
     * @param userId 用户ID
     * @return 消费总额
     */
    public java.math.BigDecimal sumUserConsumption(Long userId);

    /**
     * 统计用户某类型消费总额
     * 
     * @param userId 用户ID
     * @param consumptionType 消费类型
     * @return 消费总额
     */
    public java.math.BigDecimal sumUserConsumptionByType(Long userId, String consumptionType);

    /**
     * 统计用户消费次数
     * 
     * @param userId 用户ID
     * @return 消费次数
     */
    public int countUserConsumption(Long userId);

    /**
     * 统计某类型消费次数
     * 
     * @param userId 用户ID
     * @param consumptionType 消费类型
     * @return 消费次数
     */
    public int countUserConsumptionByType(Long userId, String consumptionType);

    /**
     * 查询最近消费记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 消费记录列表
     */
    public List<RecConsumptionRecord> selectRecentConsumptionRecords(Long userId, int limit);
}
