package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecJobApplication;

/**
 * 求职申请表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RecJobApplicationMapper 
{
    /**
     * 查询求职申请表
     * 
     * @param applicationId 求职申请表主键
     * @return 求职申请表
     */
    public RecJobApplication selectRecJobApplicationByApplicationId(Long applicationId);

    /**
     * 查询求职申请表列表
     * 
     * @param recJobApplication 求职申请表
     * @return 求职申请表集合
     */
    public List<RecJobApplication> selectRecJobApplicationList(RecJobApplication recJobApplication);

    /**
     * 根据用户ID查询申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    public List<RecJobApplication> selectRecJobApplicationByUserId(Long userId);

    /**
     * 根据商家ID查询申请列表
     * 
     * @param merchantId 商家ID
     * @return 申请列表
     */
    public List<RecJobApplication> selectRecJobApplicationByMerchantId(Long merchantId);

    /**
     * 根据职位ID查询申请列表
     * 
     * @param jobId 职位ID
     * @return 申请列表
     */
    public List<RecJobApplication> selectRecJobApplicationByJobId(Long jobId);

    /**
     * 新增求职申请表
     * 
     * @param recJobApplication 求职申请表
     * @return 结果
     */
    public int insertRecJobApplication(RecJobApplication recJobApplication);

    /**
     * 修改求职申请表
     * 
     * @param recJobApplication 求职申请表
     * @return 结果
     */
    public int updateRecJobApplication(RecJobApplication recJobApplication);

    /**
     * 删除求职申请表
     * 
     * @param applicationId 求职申请表主键
     * @return 结果
     */
    public int deleteRecJobApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除求职申请表
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecJobApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 检查用户是否已申请该职位
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 申请记录
     */
    public RecJobApplication checkUserJobApplication(Long userId, Long jobId);

    /**
     * 更新申请状态
     * 
     * @param applicationId 申请ID
     * @param status 状态
     * @return 结果
     */
    public int updateApplicationStatus(Long applicationId, String status);

    /**
     * 统计用户申请次数
     * 
     * @param userId 用户ID
     * @return 申请次数
     */
    public int countUserApplications(Long userId);

    /**
     * 统计职位申请次数
     * 
     * @param jobId 职位ID
     * @return 申请次数
     */
    public int countJobApplications(Long jobId);

    /**
     * 统计商家收到的申请次数
     * 
     * @param merchantId 商家ID
     * @return 申请次数
     */
    public int countMerchantApplications(Long merchantId);
}
