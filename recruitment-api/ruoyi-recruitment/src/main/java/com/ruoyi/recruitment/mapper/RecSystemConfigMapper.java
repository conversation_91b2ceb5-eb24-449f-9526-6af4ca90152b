package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecSystemConfig;

/**
 * 系统配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RecSystemConfigMapper 
{
    /**
     * 查询系统配置
     * 
     * @param configId 系统配置主键
     * @return 系统配置
     */
    public RecSystemConfig selectRecSystemConfigByConfigId(Long configId);

    /**
     * 查询系统配置列表
     * 
     * @param recSystemConfig 系统配置
     * @return 系统配置集合
     */
    public List<RecSystemConfig> selectRecSystemConfigList(RecSystemConfig recSystemConfig);

    /**
     * 新增系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    public int insertRecSystemConfig(RecSystemConfig recSystemConfig);

    /**
     * 修改系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    public int updateRecSystemConfig(RecSystemConfig recSystemConfig);

    /**
     * 删除系统配置
     * 
     * @param configId 系统配置主键
     * @return 结果
     */
    public int deleteRecSystemConfigByConfigId(Long configId);

    /**
     * 批量删除系统配置
     * 
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecSystemConfigByConfigIds(Long[] configIds);

    /**
     * 根据配置键查询配置信息
     * 
     * @param configKey 配置键
     * @return 系统配置
     */
    public RecSystemConfig selectRecSystemConfigByKey(String configKey);

    /**
     * 更新配置值
     *
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    public int updateConfigByKey(RecSystemConfig recSystemConfig);

    /**
     * 获取所有配置分组
     *
     * @return 分组列表
     */
    public List<String> selectConfigGroups();

    /**
     * 根据分组获取配置列表
     *
     * @param groupName 分组名称
     * @return 配置列表
     */
    public List<RecSystemConfig> selectConfigsByGroup(String groupName);
}
