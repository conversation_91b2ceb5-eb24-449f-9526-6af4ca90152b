package com.ruoyi.recruitment.service.impl;

import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.recruitment.mapper.RecOrderMapper;
import com.ruoyi.recruitment.domain.RecOrder;
import com.ruoyi.recruitment.service.IRecOrderService;
import com.ruoyi.recruitment.service.IRecSystemConfigService;
import com.ruoyi.recruitment.service.IRecConsumptionRecordService;
import com.ruoyi.recruitment.service.IRecUserProfileService;

/**
 * 订单表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecOrderServiceImpl implements IRecOrderService 
{
    @Autowired
    private RecOrderMapper recOrderMapper;
    
    @Autowired
    private IRecSystemConfigService systemConfigService;
    
    @Autowired
    private IRecConsumptionRecordService consumptionRecordService;
    
    @Autowired
    private IRecUserProfileService userProfileService;

    /**
     * 查询订单表
     * 
     * @param orderId 订单表主键
     * @return 订单表
     */
    @Override
    public RecOrder selectRecOrderByOrderId(Long orderId)
    {
        return recOrderMapper.selectRecOrderByOrderId(orderId);
    }

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单表
     */
    @Override
    public RecOrder selectRecOrderByOrderNo(String orderNo)
    {
        return recOrderMapper.selectRecOrderByOrderNo(orderNo);
    }

    /**
     * 查询订单表列表
     * 
     * @param recOrder 订单表
     * @return 订单表
     */
    @Override
    public List<RecOrder> selectRecOrderList(RecOrder recOrder)
    {
        return recOrderMapper.selectRecOrderList(recOrder);
    }

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单列表
     */
    @Override
    public List<RecOrder> selectRecOrderByUserId(Long userId)
    {
        return recOrderMapper.selectRecOrderByUserId(userId);
    }

    /**
     * 新增订单表
     * 
     * @param recOrder 订单表
     * @return 结果
     */
    @Override
    public int insertRecOrder(RecOrder recOrder)
    {
        recOrder.setCreateTime(DateUtils.getNowDate());
        return recOrderMapper.insertRecOrder(recOrder);
    }

    /**
     * 修改订单表
     * 
     * @param recOrder 订单表
     * @return 结果
     */
    @Override
    public int updateRecOrder(RecOrder recOrder)
    {
        recOrder.setUpdateTime(DateUtils.getNowDate());
        return recOrderMapper.updateRecOrder(recOrder);
    }

    /**
     * 批量删除订单表
     * 
     * @param orderIds 需要删除的订单表主键
     * @return 结果
     */
    @Override
    public int deleteRecOrderByOrderIds(Long[] orderIds)
    {
        return recOrderMapper.deleteRecOrderByOrderIds(orderIds);
    }

    /**
     * 删除订单表信息
     * 
     * @param orderId 订单表主键
     * @return 结果
     */
    @Override
    public int deleteRecOrderByOrderId(Long orderId)
    {
        return recOrderMapper.deleteRecOrderByOrderId(orderId);
    }

    /**
     * 创建查看联系方式订单
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 订单
     */
    @Override
    public RecOrder createViewContactOrder(Long userId, Long jobId)
    {
        // 获取查看联系方式价格
        String priceStr = systemConfigService.selectConfigByKey("view_contact_price");
        BigDecimal amount = new BigDecimal(priceStr != null ? priceStr : "5.00");
        
        RecOrder order = new RecOrder();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setOrderType("1"); // 查看联系方式
        order.setProductName("查看联系方式");
        order.setAmount(amount);
        order.setPayStatus("0"); // 待支付
        order.setRefundStatus("0"); // 无退款
        order.setStatus("0"); // 正常
        
        // 设置过期时间（30分钟后过期）
        Date expireTime = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        order.setExpireTime(expireTime);
        
        insertRecOrder(order);
        return order;
    }

    /**
     * 创建VIP会员订单
     * 
     * @param userId 用户ID
     * @param vipType VIP类型（月费/年费）
     * @return 订单
     */
    @Override
    public RecOrder createVipOrder(Long userId, String vipType)
    {
        String configKey = "month".equals(vipType) ? "vip_month_price" : "vip_year_price";
        String productName = "month".equals(vipType) ? "VIP会员月费" : "VIP会员年费";
        
        String priceStr = systemConfigService.selectConfigByKey(configKey);
        BigDecimal amount = new BigDecimal(priceStr != null ? priceStr : ("month".equals(vipType) ? "29.00" : "299.00"));
        
        RecOrder order = new RecOrder();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setOrderType("2"); // VIP会员
        order.setProductName(productName);
        order.setAmount(amount);
        order.setPayStatus("0"); // 待支付
        order.setRefundStatus("0"); // 无退款
        order.setStatus("0"); // 正常
        
        // 设置过期时间（30分钟后过期）
        Date expireTime = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        order.setExpireTime(expireTime);
        
        insertRecOrder(order);
        return order;
    }

    /**
     * 支付订单
     * 
     * @param orderNo 订单号
     * @param payMethod 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    @Override
    public int payOrder(String orderNo, String payMethod, String transactionId)
    {
        RecOrder order = selectRecOrderByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (!"0".equals(order.getPayStatus())) {
            throw new RuntimeException("订单状态异常");
        }
        
        Date now = new Date();
        int result = recOrderMapper.updateOrderPayStatus(orderNo, "1", now, transactionId);
        
        if (result > 0) {
            // 创建消费记录
            String description = order.getProductName();
            if ("1".equals(order.getOrderType())) {
                consumptionRecordService.createViewContactRecord(order.getUserId(), order.getOrderId(), order.getAmount(), description);
                // 增加查看联系方式次数
                userProfileService.incrementViewContactCount(order.getUserId());
            } else if ("2".equals(order.getOrderType())) {
                consumptionRecordService.createVipPurchaseRecord(order.getUserId(), order.getOrderId(), order.getAmount(), description);
                // 更新VIP状态
                Date vipExpireTime;
                if (order.getProductName().contains("月费")) {
                    vipExpireTime = new Date(now.getTime() + 30L * 24 * 60 * 60 * 1000); // 30天
                } else {
                    vipExpireTime = new Date(now.getTime() + 365L * 24 * 60 * 60 * 1000); // 365天
                }
                userProfileService.updateUserVipStatus(order.getUserId(), "1", vipExpireTime);
            }
        }
        
        return result;
    }

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int cancelOrder(Long orderId)
    {
        RecOrder order = new RecOrder();
        order.setOrderId(orderId);
        order.setStatus("1"); // 取消
        return updateRecOrder(order);
    }

    /**
     * 申请退款
     * 
     * @param orderId 订单ID
     * @param refundReason 退款原因
     * @return 结果
     */
    @Override
    public int applyRefund(Long orderId, String refundReason)
    {
        return recOrderMapper.updateOrderRefundStatus(orderId, "1", new Date(), null, refundReason);
    }

    /**
     * 处理退款
     * 
     * @param orderId 订单ID
     * @param refundAmount 退款金额
     * @return 结果
     */
    @Override
    public int processRefund(Long orderId, BigDecimal refundAmount)
    {
        int result = recOrderMapper.updateOrderRefundStatus(orderId, "2", new Date(), refundAmount, null);
        
        if (result > 0) {
            // 更新支付状态为已退款
            RecOrder order = new RecOrder();
            order.setOrderId(orderId);
            order.setPayStatus("2"); // 已退款
            updateRecOrder(order);
        }
        
        return result;
    }

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    @Override
    public String generateOrderNo()
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.valueOf((int)(Math.random() * 9000) + 1000);
        return "REC" + timestamp + random;
    }

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @return 订单数量
     */
    @Override
    public int countUserOrders(Long userId)
    {
        return recOrderMapper.countUserOrders(userId);
    }

    /**
     * 统计用户消费金额
     * 
     * @param userId 用户ID
     * @return 消费金额
     */
    @Override
    public BigDecimal sumUserOrderAmount(Long userId)
    {
        BigDecimal amount = recOrderMapper.sumUserOrderAmount(userId);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    /**
     * 查询待支付订单
     * 
     * @return 待支付订单列表
     */
    @Override
    public List<RecOrder> selectPendingPaymentOrders()
    {
        return recOrderMapper.selectPendingPaymentOrders();
    }

    /**
     * 查询已支付订单
     * 
     * @return 已支付订单列表
     */
    @Override
    public List<RecOrder> selectPaidOrders()
    {
        return recOrderMapper.selectPaidOrders();
    }

    /**
     * 查询退款订单
     * 
     * @return 退款订单列表
     */
    @Override
    public List<RecOrder> selectRefundOrders()
    {
        return recOrderMapper.selectRefundOrders();
    }

    /**
     * 自动取消过期订单
     * 
     * @return 取消数量
     */
    @Override
    public int autoCancelExpiredOrders()
    {
        List<RecOrder> expiredOrders = recOrderMapper.selectExpiredOrders();
        int count = 0;
        for (RecOrder order : expiredOrders) {
            if (cancelOrder(order.getOrderId()) > 0) {
                count++;
            }
        }
        return count;
    }
}
