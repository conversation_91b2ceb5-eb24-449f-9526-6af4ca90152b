package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecUserProfile;
import com.ruoyi.recruitment.service.IRecUserProfileService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 用户扩展表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/userProfile")
public class RecUserProfileController extends BaseController
{
    @Autowired
    private IRecUserProfileService recUserProfileService;

    /**
     * 查询用户扩展表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecUserProfile recUserProfile)
    {
        startPage();
        List<RecUserProfile> list = recUserProfileService.selectRecUserProfileList(recUserProfile);
        return getDataTable(list);
    }

    /**
     * 导出用户扩展表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:export')")
    @Log(title = "用户扩展表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecUserProfile recUserProfile)
    {
        List<RecUserProfile> list = recUserProfileService.selectRecUserProfileList(recUserProfile);
        ExcelUtil<RecUserProfile> util = new ExcelUtil<RecUserProfile>(RecUserProfile.class);
        util.exportExcel(response, list, "用户扩展表数据");
    }

    /**
     * 获取用户扩展表详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:query')")
    @GetMapping(value = "/{profileId}")
    public AjaxResult getInfo(@PathVariable("profileId") Long profileId)
    {
        return success(recUserProfileService.selectRecUserProfileByProfileId(profileId));
    }

    /**
     * 新增用户扩展表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:add')")
    @Log(title = "用户扩展表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecUserProfile recUserProfile)
    {
        return toAjax(recUserProfileService.insertRecUserProfile(recUserProfile));
    }

    /**
     * 修改用户扩展表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:edit')")
    @Log(title = "用户扩展表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecUserProfile recUserProfile)
    {
        return toAjax(recUserProfileService.updateRecUserProfile(recUserProfile));
    }

    /**
     * 删除用户扩展表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:remove')")
    @Log(title = "用户扩展表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{profileIds}")
    public AjaxResult remove(@PathVariable Long[] profileIds)
    {
        return toAjax(recUserProfileService.deleteRecUserProfileByProfileIds(profileIds));
    }

    /**
     * 获取当前用户档案
     */
    @GetMapping("/profile")
    public AjaxResult getProfile()
    {
        Long userId = SecurityUtils.getUserId();
        RecUserProfile profile = recUserProfileService.selectRecUserProfileByUserId(userId);
        return success(profile);
    }

    /**
     * 更新当前用户档案
     */
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody RecUserProfile recUserProfile)
    {
        Long userId = SecurityUtils.getUserId();
        recUserProfile.setUserId(userId);
        return toAjax(recUserProfileService.createOrUpdateUserProfile(recUserProfile));
    }

    /**
     * 冻结用户账户
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:freeze')")
    @Log(title = "冻结用户", businessType = BusinessType.UPDATE)
    @PutMapping("/freeze/{userId}")
    public AjaxResult freezeUser(@PathVariable Long userId)
    {
        return toAjax(recUserProfileService.freezeUser(userId));
    }

    /**
     * 解冻用户账户
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:unfreeze')")
    @Log(title = "解冻用户", businessType = BusinessType.UPDATE)
    @PutMapping("/unfreeze/{userId}")
    public AjaxResult unfreezeUser(@PathVariable Long userId)
    {
        return toAjax(recUserProfileService.unfreezeUser(userId));
    }

    /**
     * 查询VIP用户列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:vip')")
    @GetMapping("/vip")
    public TableDataInfo getVipUsers()
    {
        startPage();
        List<RecUserProfile> list = recUserProfileService.selectVipUsers();
        return getDataTable(list);
    }

    /**
     * 查询即将过期的VIP用户
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:vip')")
    @GetMapping("/vip/expiring/{days}")
    public TableDataInfo getExpiringSoonVipUsers(@PathVariable int days)
    {
        startPage();
        List<RecUserProfile> list = recUserProfileService.selectExpiringSoonVipUsers(days);
        return getDataTable(list);
    }

    /**
     * 检查当前用户是否为VIP
     */
    @GetMapping("/vip/check")
    public AjaxResult checkVip()
    {
        Long userId = SecurityUtils.getUserId();
        boolean isVip = recUserProfileService.isUserVip(userId);
        return success(isVip);
    }

    /**
     * 手动处理过期VIP用户
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:vip')")
    @Log(title = "处理过期VIP", businessType = BusinessType.UPDATE)
    @PostMapping("/vip/processExpired")
    public AjaxResult processExpiredVipUsers()
    {
        int count = recUserProfileService.processExpiredVipUsers();
        return success("已处理 " + count + " 个过期VIP用户");
    }

    /**
     * 根据用户ID查询用户档案
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:query')")
    @GetMapping("/user/{userId}")
    public AjaxResult getUserProfile(@PathVariable Long userId)
    {
        RecUserProfile profile = recUserProfileService.selectRecUserProfileByUserId(userId);
        return success(profile);
    }

    /**
     * 更新用户VIP状态
     */
    @PreAuthorize("@ss.hasPermi('recruitment:userProfile:vip')")
    @Log(title = "更新VIP状态", businessType = BusinessType.UPDATE)
    @PutMapping("/vip/{userId}")
    public AjaxResult updateVipStatus(@PathVariable Long userId, @RequestBody RecUserProfile recUserProfile)
    {
        return toAjax(recUserProfileService.updateUserVipStatus(userId, 
            recUserProfile.getIsVip(), recUserProfile.getVipExpireTime()));
    }
}
