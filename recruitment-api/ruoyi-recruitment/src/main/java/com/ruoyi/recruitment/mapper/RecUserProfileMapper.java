package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecUserProfile;

/**
 * 用户扩展表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RecUserProfileMapper 
{
    /**
     * 查询用户扩展表
     * 
     * @param profileId 用户扩展表主键
     * @return 用户扩展表
     */
    public RecUserProfile selectRecUserProfileByProfileId(Long profileId);

    /**
     * 根据用户ID查询用户扩展表
     * 
     * @param userId 用户ID
     * @return 用户扩展表
     */
    public RecUserProfile selectRecUserProfileByUserId(Long userId);

    /**
     * 查询用户扩展表列表
     * 
     * @param recUserProfile 用户扩展表
     * @return 用户扩展表集合
     */
    public List<RecUserProfile> selectRecUserProfileList(RecUserProfile recUserProfile);

    /**
     * 新增用户扩展表
     * 
     * @param recUserProfile 用户扩展表
     * @return 结果
     */
    public int insertRecUserProfile(RecUserProfile recUserProfile);

    /**
     * 修改用户扩展表
     * 
     * @param recUserProfile 用户扩展表
     * @return 结果
     */
    public int updateRecUserProfile(RecUserProfile recUserProfile);

    /**
     * 删除用户扩展表
     * 
     * @param profileId 用户扩展表主键
     * @return 结果
     */
    public int deleteRecUserProfileByProfileId(Long profileId);

    /**
     * 批量删除用户扩展表
     * 
     * @param profileIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecUserProfileByProfileIds(Long[] profileIds);

    /**
     * 根据用户ID删除用户扩展表
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteRecUserProfileByUserId(Long userId);

    /**
     * 更新用户VIP状态
     * 
     * @param userId 用户ID
     * @param isVip VIP状态
     * @param vipExpireTime VIP到期时间
     * @return 结果
     */
    public int updateUserVipStatus(Long userId, String isVip, java.util.Date vipExpireTime);

    /**
     * 增加查看联系方式次数
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int incrementViewContactCount(Long userId);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 结果
     */
    public int updateUserStatus(Long userId, String status);

    /**
     * 查询VIP用户列表
     * 
     * @return VIP用户列表
     */
    public List<RecUserProfile> selectVipUsers();

    /**
     * 查询即将过期的VIP用户
     * 
     * @param days 天数
     * @return VIP用户列表
     */
    public List<RecUserProfile> selectExpiringSoonVipUsers(int days);
}
