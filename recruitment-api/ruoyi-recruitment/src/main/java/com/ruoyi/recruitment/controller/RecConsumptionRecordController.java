package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecConsumptionRecord;
import com.ruoyi.recruitment.service.IRecConsumptionRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 消费记录表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/consumptionRecord")
public class RecConsumptionRecordController extends BaseController
{
    @Autowired
    private IRecConsumptionRecordService recConsumptionRecordService;

    /**
     * 查询消费记录表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecConsumptionRecord recConsumptionRecord)
    {
        startPage();
        List<RecConsumptionRecord> list = recConsumptionRecordService.selectRecConsumptionRecordList(recConsumptionRecord);
        return getDataTable(list);
    }

    /**
     * 导出消费记录表列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:export')")
    @Log(title = "消费记录表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecConsumptionRecord recConsumptionRecord)
    {
        List<RecConsumptionRecord> list = recConsumptionRecordService.selectRecConsumptionRecordList(recConsumptionRecord);
        ExcelUtil<RecConsumptionRecord> util = new ExcelUtil<RecConsumptionRecord>(RecConsumptionRecord.class);
        util.exportExcel(response, list, "消费记录表数据");
    }

    /**
     * 获取消费记录表详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:query')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return success(recConsumptionRecordService.selectRecConsumptionRecordByRecordId(recordId));
    }

    /**
     * 新增消费记录表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:add')")
    @Log(title = "消费记录表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecConsumptionRecord recConsumptionRecord)
    {
        return toAjax(recConsumptionRecordService.insertRecConsumptionRecord(recConsumptionRecord));
    }

    /**
     * 修改消费记录表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:edit')")
    @Log(title = "消费记录表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecConsumptionRecord recConsumptionRecord)
    {
        return toAjax(recConsumptionRecordService.updateRecConsumptionRecord(recConsumptionRecord));
    }

    /**
     * 删除消费记录表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:remove')")
    @Log(title = "消费记录表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(recConsumptionRecordService.deleteRecConsumptionRecordByRecordIds(recordIds));
    }

    /**
     * 获取当前用户的消费记录列表
     */
    @GetMapping("/my")
    public TableDataInfo getMyConsumptionRecords()
    {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<RecConsumptionRecord> list = recConsumptionRecordService.selectRecConsumptionRecordByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 根据用户ID查询消费记录列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:consumptionRecord:user')")
    @GetMapping("/user/{userId}")
    public TableDataInfo getConsumptionRecordsByUser(@PathVariable Long userId)
    {
        startPage();
        List<RecConsumptionRecord> list = recConsumptionRecordService.selectRecConsumptionRecordByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 统计用户消费总额
     */
    @GetMapping("/sum/user/{userId}")
    public AjaxResult sumUserConsumption(@PathVariable Long userId)
    {
        java.math.BigDecimal amount = recConsumptionRecordService.sumUserConsumption(userId);
        return success(amount);
    }

    /**
     * 统计用户某类型消费总额
     */
    @GetMapping("/sum/user/{userId}/type/{consumptionType}")
    public AjaxResult sumUserConsumptionByType(@PathVariable Long userId, @PathVariable String consumptionType)
    {
        java.math.BigDecimal amount = recConsumptionRecordService.sumUserConsumptionByType(userId, consumptionType);
        return success(amount);
    }

    /**
     * 统计用户消费次数
     */
    @GetMapping("/count/user/{userId}")
    public AjaxResult countUserConsumption(@PathVariable Long userId)
    {
        int count = recConsumptionRecordService.countUserConsumption(userId);
        return success(count);
    }

    /**
     * 统计某类型消费次数
     */
    @GetMapping("/count/user/{userId}/type/{consumptionType}")
    public AjaxResult countUserConsumptionByType(@PathVariable Long userId, @PathVariable String consumptionType)
    {
        int count = recConsumptionRecordService.countUserConsumptionByType(userId, consumptionType);
        return success(count);
    }

    /**
     * 查询最近消费记录
     */
    @GetMapping("/recent/user/{userId}/{limit}")
    public AjaxResult getRecentConsumptionRecords(@PathVariable Long userId, @PathVariable int limit)
    {
        List<RecConsumptionRecord> list = recConsumptionRecordService.selectRecentConsumptionRecords(userId, limit);
        return success(list);
    }

    /**
     * 统计当前用户消费总额
     */
    @GetMapping("/sum/my")
    public AjaxResult sumMyConsumption()
    {
        Long userId = SecurityUtils.getUserId();
        java.math.BigDecimal amount = recConsumptionRecordService.sumUserConsumption(userId);
        return success(amount);
    }

    /**
     * 统计当前用户某类型消费总额
     */
    @GetMapping("/sum/my/type/{consumptionType}")
    public AjaxResult sumMyConsumptionByType(@PathVariable String consumptionType)
    {
        Long userId = SecurityUtils.getUserId();
        java.math.BigDecimal amount = recConsumptionRecordService.sumUserConsumptionByType(userId, consumptionType);
        return success(amount);
    }

    /**
     * 统计当前用户消费次数
     */
    @GetMapping("/count/my")
    public AjaxResult countMyConsumption()
    {
        Long userId = SecurityUtils.getUserId();
        int count = recConsumptionRecordService.countUserConsumption(userId);
        return success(count);
    }

    /**
     * 统计当前用户某类型消费次数
     */
    @GetMapping("/count/my/type/{consumptionType}")
    public AjaxResult countMyConsumptionByType(@PathVariable String consumptionType)
    {
        Long userId = SecurityUtils.getUserId();
        int count = recConsumptionRecordService.countUserConsumptionByType(userId, consumptionType);
        return success(count);
    }

    /**
     * 查询当前用户最近消费记录
     */
    @GetMapping("/recent/my/{limit}")
    public AjaxResult getMyRecentConsumptionRecords(@PathVariable int limit)
    {
        Long userId = SecurityUtils.getUserId();
        List<RecConsumptionRecord> list = recConsumptionRecordService.selectRecentConsumptionRecords(userId, limit);
        return success(list);
    }
}
