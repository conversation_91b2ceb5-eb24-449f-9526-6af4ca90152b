package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecUserProfile;

/**
 * 用户扩展表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecUserProfileService 
{
    /**
     * 查询用户扩展表
     * 
     * @param profileId 用户扩展表主键
     * @return 用户扩展表
     */
    public RecUserProfile selectRecUserProfileByProfileId(Long profileId);

    /**
     * 根据用户ID查询用户扩展表
     * 
     * @param userId 用户ID
     * @return 用户扩展表
     */
    public RecUserProfile selectRecUserProfileByUserId(Long userId);

    /**
     * 查询用户扩展表列表
     * 
     * @param recUserProfile 用户扩展表
     * @return 用户扩展表集合
     */
    public List<RecUserProfile> selectRecUserProfileList(RecUserProfile recUserProfile);

    /**
     * 新增用户扩展表
     * 
     * @param recUserProfile 用户扩展表
     * @return 结果
     */
    public int insertRecUserProfile(RecUserProfile recUserProfile);

    /**
     * 修改用户扩展表
     * 
     * @param recUserProfile 用户扩展表
     * @return 结果
     */
    public int updateRecUserProfile(RecUserProfile recUserProfile);

    /**
     * 批量删除用户扩展表
     * 
     * @param profileIds 需要删除的用户扩展表主键集合
     * @return 结果
     */
    public int deleteRecUserProfileByProfileIds(Long[] profileIds);

    /**
     * 删除用户扩展表信息
     * 
     * @param profileId 用户扩展表主键
     * @return 结果
     */
    public int deleteRecUserProfileByProfileId(Long profileId);

    /**
     * 根据用户ID删除用户扩展表
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteRecUserProfileByUserId(Long userId);

    /**
     * 创建或更新用户档案
     * 
     * @param recUserProfile 用户档案
     * @return 结果
     */
    public int createOrUpdateUserProfile(RecUserProfile recUserProfile);

    /**
     * 更新用户VIP状态
     * 
     * @param userId 用户ID
     * @param isVip VIP状态
     * @param vipExpireTime VIP到期时间
     * @return 结果
     */
    public int updateUserVipStatus(Long userId, String isVip, java.util.Date vipExpireTime);

    /**
     * 增加查看联系方式次数
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int incrementViewContactCount(Long userId);

    /**
     * 冻结用户账户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int freezeUser(Long userId);

    /**
     * 解冻用户账户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int unfreezeUser(Long userId);

    /**
     * 查询VIP用户列表
     * 
     * @return VIP用户列表
     */
    public List<RecUserProfile> selectVipUsers();

    /**
     * 查询即将过期的VIP用户
     * 
     * @param days 天数
     * @return VIP用户列表
     */
    public List<RecUserProfile> selectExpiringSoonVipUsers(int days);

    /**
     * 检查用户是否为VIP
     * 
     * @param userId 用户ID
     * @return 是否为VIP
     */
    public boolean isUserVip(Long userId);

    /**
     * 检查用户VIP是否过期
     * 
     * @param userId 用户ID
     * @return 是否过期
     */
    public boolean isUserVipExpired(Long userId);

    /**
     * 自动处理过期VIP用户
     * 
     * @return 处理数量
     */
    public int processExpiredVipUsers();
}
