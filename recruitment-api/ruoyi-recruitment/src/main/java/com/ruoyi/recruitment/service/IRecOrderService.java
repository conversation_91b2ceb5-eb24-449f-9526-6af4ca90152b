package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecOrder;

/**
 * 订单表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecOrderService 
{
    /**
     * 查询订单表
     * 
     * @param orderId 订单表主键
     * @return 订单表
     */
    public RecOrder selectRecOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单表
     */
    public RecOrder selectRecOrderByOrderNo(String orderNo);

    /**
     * 查询订单表列表
     * 
     * @param recOrder 订单表
     * @return 订单表集合
     */
    public List<RecOrder> selectRecOrderList(RecOrder recOrder);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单列表
     */
    public List<RecOrder> selectRecOrderByUserId(Long userId);

    /**
     * 新增订单表
     * 
     * @param recOrder 订单表
     * @return 结果
     */
    public int insertRecOrder(RecOrder recOrder);

    /**
     * 修改订单表
     * 
     * @param recOrder 订单表
     * @return 结果
     */
    public int updateRecOrder(RecOrder recOrder);

    /**
     * 批量删除订单表
     * 
     * @param orderIds 需要删除的订单表主键集合
     * @return 结果
     */
    public int deleteRecOrderByOrderIds(Long[] orderIds);

    /**
     * 删除订单表信息
     * 
     * @param orderId 订单表主键
     * @return 结果
     */
    public int deleteRecOrderByOrderId(Long orderId);

    /**
     * 创建查看联系方式订单
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 订单
     */
    public RecOrder createViewContactOrder(Long userId, Long jobId);

    /**
     * 创建VIP会员订单
     * 
     * @param userId 用户ID
     * @param vipType VIP类型（月费/年费）
     * @return 订单
     */
    public RecOrder createVipOrder(Long userId, String vipType);

    /**
     * 支付订单
     * 
     * @param orderNo 订单号
     * @param payMethod 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    public int payOrder(String orderNo, String payMethod, String transactionId);

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int cancelOrder(Long orderId);

    /**
     * 申请退款
     * 
     * @param orderId 订单ID
     * @param refundReason 退款原因
     * @return 结果
     */
    public int applyRefund(Long orderId, String refundReason);

    /**
     * 处理退款
     * 
     * @param orderId 订单ID
     * @param refundAmount 退款金额
     * @return 结果
     */
    public int processRefund(Long orderId, java.math.BigDecimal refundAmount);

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    public String generateOrderNo();

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @return 订单数量
     */
    public int countUserOrders(Long userId);

    /**
     * 统计用户消费金额
     * 
     * @param userId 用户ID
     * @return 消费金额
     */
    public java.math.BigDecimal sumUserOrderAmount(Long userId);

    /**
     * 查询待支付订单
     * 
     * @return 待支付订单列表
     */
    public List<RecOrder> selectPendingPaymentOrders();

    /**
     * 查询已支付订单
     * 
     * @return 已支付订单列表
     */
    public List<RecOrder> selectPaidOrders();

    /**
     * 查询退款订单
     * 
     * @return 退款订单列表
     */
    public List<RecOrder> selectRefundOrders();

    /**
     * 自动取消过期订单
     * 
     * @return 取消数量
     */
    public int autoCancelExpiredOrders();
}
