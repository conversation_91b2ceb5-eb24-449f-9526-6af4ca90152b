package com.ruoyi.recruitment.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.recruitment.mapper.RecUserProfileMapper;
import com.ruoyi.recruitment.domain.RecUserProfile;
import com.ruoyi.recruitment.service.IRecUserProfileService;

/**
 * 用户扩展表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecUserProfileServiceImpl implements IRecUserProfileService 
{
    @Autowired
    private RecUserProfileMapper recUserProfileMapper;

    /**
     * 查询用户扩展表
     * 
     * @param profileId 用户扩展表主键
     * @return 用户扩展表
     */
    @Override
    public RecUserProfile selectRecUserProfileByProfileId(Long profileId)
    {
        return recUserProfileMapper.selectRecUserProfileByProfileId(profileId);
    }

    /**
     * 根据用户ID查询用户扩展表
     * 
     * @param userId 用户ID
     * @return 用户扩展表
     */
    @Override
    public RecUserProfile selectRecUserProfileByUserId(Long userId)
    {
        return recUserProfileMapper.selectRecUserProfileByUserId(userId);
    }

    /**
     * 查询用户扩展表列表
     * 
     * @param recUserProfile 用户扩展表
     * @return 用户扩展表
     */
    @Override
    public List<RecUserProfile> selectRecUserProfileList(RecUserProfile recUserProfile)
    {
        return recUserProfileMapper.selectRecUserProfileList(recUserProfile);
    }

    /**
     * 新增用户扩展表
     * 
     * @param recUserProfile 用户扩展表
     * @return 结果
     */
    @Override
    public int insertRecUserProfile(RecUserProfile recUserProfile)
    {
        recUserProfile.setCreateTime(DateUtils.getNowDate());
        return recUserProfileMapper.insertRecUserProfile(recUserProfile);
    }

    /**
     * 修改用户扩展表
     * 
     * @param recUserProfile 用户扩展表
     * @return 结果
     */
    @Override
    public int updateRecUserProfile(RecUserProfile recUserProfile)
    {
        recUserProfile.setUpdateTime(DateUtils.getNowDate());
        return recUserProfileMapper.updateRecUserProfile(recUserProfile);
    }

    /**
     * 批量删除用户扩展表
     * 
     * @param profileIds 需要删除的用户扩展表主键
     * @return 结果
     */
    @Override
    public int deleteRecUserProfileByProfileIds(Long[] profileIds)
    {
        return recUserProfileMapper.deleteRecUserProfileByProfileIds(profileIds);
    }

    /**
     * 删除用户扩展表信息
     * 
     * @param profileId 用户扩展表主键
     * @return 结果
     */
    @Override
    public int deleteRecUserProfileByProfileId(Long profileId)
    {
        return recUserProfileMapper.deleteRecUserProfileByProfileId(profileId);
    }

    /**
     * 根据用户ID删除用户扩展表
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteRecUserProfileByUserId(Long userId)
    {
        return recUserProfileMapper.deleteRecUserProfileByUserId(userId);
    }

    /**
     * 创建或更新用户档案
     * 
     * @param recUserProfile 用户档案
     * @return 结果
     */
    @Override
    public int createOrUpdateUserProfile(RecUserProfile recUserProfile)
    {
        RecUserProfile existProfile = recUserProfileMapper.selectRecUserProfileByUserId(recUserProfile.getUserId());
        if (existProfile != null) {
            recUserProfile.setProfileId(existProfile.getProfileId());
            return updateRecUserProfile(recUserProfile);
        } else {
            // 设置默认值
            if (recUserProfile.getStatus() == null) {
                recUserProfile.setStatus("0"); // 正常状态
            }
            if (recUserProfile.getIsVip() == null) {
                recUserProfile.setIsVip("0"); // 非VIP
            }
            if (recUserProfile.getViewContactCount() == null) {
                recUserProfile.setViewContactCount(0);
            }
            return insertRecUserProfile(recUserProfile);
        }
    }

    /**
     * 更新用户VIP状态
     * 
     * @param userId 用户ID
     * @param isVip VIP状态
     * @param vipExpireTime VIP到期时间
     * @return 结果
     */
    @Override
    public int updateUserVipStatus(Long userId, String isVip, Date vipExpireTime)
    {
        return recUserProfileMapper.updateUserVipStatus(userId, isVip, vipExpireTime);
    }

    /**
     * 增加查看联系方式次数
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int incrementViewContactCount(Long userId)
    {
        return recUserProfileMapper.incrementViewContactCount(userId);
    }

    /**
     * 冻结用户账户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int freezeUser(Long userId)
    {
        return recUserProfileMapper.updateUserStatus(userId, "1");
    }

    /**
     * 解冻用户账户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int unfreezeUser(Long userId)
    {
        return recUserProfileMapper.updateUserStatus(userId, "0");
    }

    /**
     * 查询VIP用户列表
     * 
     * @return VIP用户列表
     */
    @Override
    public List<RecUserProfile> selectVipUsers()
    {
        return recUserProfileMapper.selectVipUsers();
    }

    /**
     * 查询即将过期的VIP用户
     * 
     * @param days 天数
     * @return VIP用户列表
     */
    @Override
    public List<RecUserProfile> selectExpiringSoonVipUsers(int days)
    {
        return recUserProfileMapper.selectExpiringSoonVipUsers(days);
    }

    /**
     * 检查用户是否为VIP
     * 
     * @param userId 用户ID
     * @return 是否为VIP
     */
    @Override
    public boolean isUserVip(Long userId)
    {
        RecUserProfile profile = recUserProfileMapper.selectRecUserProfileByUserId(userId);
        if (profile == null) {
            return false;
        }
        
        if (!"1".equals(profile.getIsVip())) {
            return false;
        }
        
        // 检查是否过期
        if (profile.getVipExpireTime() != null && profile.getVipExpireTime().before(new Date())) {
            // VIP已过期，更新状态
            updateUserVipStatus(userId, "0", null);
            return false;
        }
        
        return true;
    }

    /**
     * 检查用户VIP是否过期
     * 
     * @param userId 用户ID
     * @return 是否过期
     */
    @Override
    public boolean isUserVipExpired(Long userId)
    {
        RecUserProfile profile = recUserProfileMapper.selectRecUserProfileByUserId(userId);
        if (profile == null || !"1".equals(profile.getIsVip())) {
            return true;
        }
        
        return profile.getVipExpireTime() != null && profile.getVipExpireTime().before(new Date());
    }

    /**
     * 自动处理过期VIP用户
     * 
     * @return 处理数量
     */
    @Override
    public int processExpiredVipUsers()
    {
        List<RecUserProfile> expiredUsers = selectExpiringSoonVipUsers(0); // 已过期的用户
        int count = 0;
        for (RecUserProfile user : expiredUsers) {
            if (updateUserVipStatus(user.getUserId(), "0", null) > 0) {
                count++;
            }
        }
        return count;
    }
}
