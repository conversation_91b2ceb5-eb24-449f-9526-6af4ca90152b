package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户扩展表对象 rec_user_profile
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public class RecUserProfile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户档案ID */
    private Long profileId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthDate;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 工作经验（年） */
    @Excel(name = "工作经验")
    private Integer workExperience;

    /** 当前薪资 */
    @Excel(name = "当前薪资")
    private BigDecimal currentSalary;

    /** 期望薪资 */
    @Excel(name = "期望薪资")
    private BigDecimal expectedSalary;

    /** 省份ID */
    @Excel(name = "省份ID")
    private Long provinceId;

    /** 城市ID */
    @Excel(name = "城市ID")
    private Long cityId;

    /** 区县ID */
    @Excel(name = "区县ID")
    private Long districtId;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 简历文件地址 */
    @Excel(name = "简历文件地址")
    private String resumeUrl;

    /** 自我介绍 */
    @Excel(name = "自我介绍")
    private String selfIntro;

    /** 技能标签 */
    @Excel(name = "技能标签")
    private String skills;

    /** 是否VIP（0否 1是） */
    @Excel(name = "是否VIP", readConverterExp = "0=否,1=是")
    private String isVip;

    /** VIP到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "VIP到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date vipExpireTime;

    /** 查看联系方式次数 */
    @Excel(name = "查看联系方式次数")
    private Integer viewContactCount;

    /** 状态（0正常 1冻结） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=冻结")
    private String status;

    /** 省份名称 */
    private String provinceName;

    /** 城市名称 */
    private String cityName;

    /** 区县名称 */
    private String districtName;

    /** 用户名 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户邮箱 */
    private String email;

    /** 手机号码 */
    private String phonenumber;

    public void setProfileId(Long profileId) 
    {
        this.profileId = profileId;
    }

    public Long getProfileId() 
    {
        return profileId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setRealName(String realName) 
    {
        this.realName = realName;
    }

    public String getRealName() 
    {
        return realName;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setBirthDate(Date birthDate) 
    {
        this.birthDate = birthDate;
    }

    public Date getBirthDate() 
    {
        return birthDate;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setWorkExperience(Integer workExperience) 
    {
        this.workExperience = workExperience;
    }

    public Integer getWorkExperience() 
    {
        return workExperience;
    }
    public void setCurrentSalary(BigDecimal currentSalary) 
    {
        this.currentSalary = currentSalary;
    }

    public BigDecimal getCurrentSalary() 
    {
        return currentSalary;
    }
    public void setExpectedSalary(BigDecimal expectedSalary) 
    {
        this.expectedSalary = expectedSalary;
    }

    public BigDecimal getExpectedSalary() 
    {
        return expectedSalary;
    }
    public void setProvinceId(Long provinceId) 
    {
        this.provinceId = provinceId;
    }

    public Long getProvinceId() 
    {
        return provinceId;
    }
    public void setCityId(Long cityId) 
    {
        this.cityId = cityId;
    }

    public Long getCityId() 
    {
        return cityId;
    }
    public void setDistrictId(Long districtId) 
    {
        this.districtId = districtId;
    }

    public Long getDistrictId() 
    {
        return districtId;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setResumeUrl(String resumeUrl) 
    {
        this.resumeUrl = resumeUrl;
    }

    public String getResumeUrl() 
    {
        return resumeUrl;
    }
    public void setSelfIntro(String selfIntro) 
    {
        this.selfIntro = selfIntro;
    }

    public String getSelfIntro() 
    {
        return selfIntro;
    }
    public void setSkills(String skills) 
    {
        this.skills = skills;
    }

    public String getSkills() 
    {
        return skills;
    }
    public void setIsVip(String isVip) 
    {
        this.isVip = isVip;
    }

    public String getIsVip() 
    {
        return isVip;
    }
    public void setVipExpireTime(Date vipExpireTime) 
    {
        this.vipExpireTime = vipExpireTime;
    }

    public Date getVipExpireTime() 
    {
        return vipExpireTime;
    }
    public void setViewContactCount(Integer viewContactCount) 
    {
        this.viewContactCount = viewContactCount;
    }

    public Integer getViewContactCount() 
    {
        return viewContactCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("profileId", getProfileId())
            .append("userId", getUserId())
            .append("realName", getRealName())
            .append("idCard", getIdCard())
            .append("birthDate", getBirthDate())
            .append("education", getEducation())
            .append("workExperience", getWorkExperience())
            .append("currentSalary", getCurrentSalary())
            .append("expectedSalary", getExpectedSalary())
            .append("provinceId", getProvinceId())
            .append("cityId", getCityId())
            .append("districtId", getDistrictId())
            .append("address", getAddress())
            .append("resumeUrl", getResumeUrl())
            .append("selfIntro", getSelfIntro())
            .append("skills", getSkills())
            .append("isVip", getIsVip())
            .append("vipExpireTime", getVipExpireTime())
            .append("viewContactCount", getViewContactCount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
