package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecJobApplication;

/**
 * 求职申请表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecJobApplicationService 
{
    /**
     * 查询求职申请表
     * 
     * @param applicationId 求职申请表主键
     * @return 求职申请表
     */
    public RecJobApplication selectRecJobApplicationByApplicationId(Long applicationId);

    /**
     * 查询求职申请表列表
     * 
     * @param recJobApplication 求职申请表
     * @return 求职申请表集合
     */
    public List<RecJobApplication> selectRecJobApplicationList(RecJobApplication recJobApplication);

    /**
     * 根据用户ID查询申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    public List<RecJobApplication> selectRecJobApplicationByUserId(Long userId);

    /**
     * 根据商家ID查询申请列表
     * 
     * @param merchantId 商家ID
     * @return 申请列表
     */
    public List<RecJobApplication> selectRecJobApplicationByMerchantId(Long merchantId);

    /**
     * 根据职位ID查询申请列表
     * 
     * @param jobId 职位ID
     * @return 申请列表
     */
    public List<RecJobApplication> selectRecJobApplicationByJobId(Long jobId);

    /**
     * 新增求职申请表
     * 
     * @param recJobApplication 求职申请表
     * @return 结果
     */
    public int insertRecJobApplication(RecJobApplication recJobApplication);

    /**
     * 修改求职申请表
     * 
     * @param recJobApplication 求职申请表
     * @return 结果
     */
    public int updateRecJobApplication(RecJobApplication recJobApplication);

    /**
     * 批量删除求职申请表
     * 
     * @param applicationIds 需要删除的求职申请表主键集合
     * @return 结果
     */
    public int deleteRecJobApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除求职申请表信息
     * 
     * @param applicationId 求职申请表主键
     * @return 结果
     */
    public int deleteRecJobApplicationByApplicationId(Long applicationId);

    /**
     * 用户申请职位
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 结果
     */
    public int applyJob(Long userId, Long jobId);

    /**
     * 检查用户是否已申请该职位
     * 
     * @param userId 用户ID
     * @param jobId 职位ID
     * @return 是否已申请
     */
    public boolean hasUserAppliedJob(Long userId, Long jobId);

    /**
     * 更新申请状态
     * 
     * @param applicationId 申请ID
     * @param status 状态
     * @return 结果
     */
    public int updateApplicationStatus(Long applicationId, String status);

    /**
     * 商家查看申请
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int merchantViewApplication(Long applicationId);

    /**
     * 商家沟通申请者
     * 
     * @param applicationId 申请ID
     * @param remark 备注
     * @return 结果
     */
    public int merchantCommunicateApplicant(Long applicationId, String remark);

    /**
     * 商家拒绝申请
     * 
     * @param applicationId 申请ID
     * @param remark 拒绝原因
     * @return 结果
     */
    public int merchantRejectApplication(Long applicationId, String remark);

    /**
     * 统计用户申请次数
     * 
     * @param userId 用户ID
     * @return 申请次数
     */
    public int countUserApplications(Long userId);

    /**
     * 统计职位申请次数
     * 
     * @param jobId 职位ID
     * @return 申请次数
     */
    public int countJobApplications(Long jobId);

    /**
     * 统计商家收到的申请次数
     * 
     * @param merchantId 商家ID
     * @return 申请次数
     */
    public int countMerchantApplications(Long merchantId);
}
