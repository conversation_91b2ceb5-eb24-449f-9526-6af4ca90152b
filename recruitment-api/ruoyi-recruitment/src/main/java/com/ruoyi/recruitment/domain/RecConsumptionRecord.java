package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 消费记录表对象 rec_consumption_record
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public class RecConsumptionRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 消费类型（1查看联系方式 2购买VIP） */
    @Excel(name = "消费类型", readConverterExp = "1=查看联系方式,2=购买VIP")
    private String consumptionType;

    /** 消费金额 */
    @Excel(name = "消费金额")
    private BigDecimal amount;

    /** 消费描述 */
    @Excel(name = "消费描述")
    private String description;

    /** 用户名 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户手机号 */
    private String phonenumber;

    /** 订单号 */
    private String orderNo;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setConsumptionType(String consumptionType) 
    {
        this.consumptionType = consumptionType;
    }

    public String getConsumptionType() 
    {
        return consumptionType;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("userId", getUserId())
            .append("orderId", getOrderId())
            .append("consumptionType", getConsumptionType())
            .append("amount", getAmount())
            .append("description", getDescription())
            .append("createTime", getCreateTime())
            .toString();
    }
}
