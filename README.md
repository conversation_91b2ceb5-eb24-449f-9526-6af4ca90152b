# 招聘平台系统

基于若依框架开发的现代化招聘平台，提供完整的求职招聘解决方案。

## 🚀 项目特色

- **现代化架构**: 基于Spring Boot + Vue.js的前后端分离架构
- **完整功能**: 涵盖用户管理、商家管理、职位发布、求职申请、订单支付等完整业务流程
- **权限控制**: 基于RBAC的细粒度权限控制系统
- **支付集成**: 支持多种支付方式的订单管理系统
- **数据统计**: 丰富的数据统计和分析功能
- **响应式设计**: 支持PC端和移动端的响应式界面

## 📋 功能模块

### 🔐 用户管理
- **用户注册登录**: 支持手机号、邮箱注册登录
- **用户档案管理**: 完整的个人信息、简历管理
- **VIP会员系统**: 会员特权、到期管理
- **账户状态管理**: 用户冻结、解冻功能

### 🏢 商家管理
- **商家注册审核**: 商家资质审核流程
- **企业信息管理**: 企业基本信息、认证状态
- **商家等级管理**: 不同等级商家权限控制

### 💼 职位管理
- **职位发布**: 丰富的职位信息编辑功能
- **职位审核**: 管理员审核机制
- **职位状态管理**: 发布、下线、过期管理
- **职位搜索**: 多维度职位搜索功能

### 📝 求职申请
- **简历投递**: 一键投递简历功能
- **申请状态跟踪**: 申请进度实时跟踪
- **沟通管理**: 商家与求职者沟通功能
- **申请统计**: 申请数据统计分析

### 💰 订单支付
- **查看联系方式**: 付费查看联系方式功能
- **VIP会员购买**: 月费、年费会员购买
- **订单管理**: 订单状态、支付记录管理
- **退款处理**: 完整的退款流程

### 📊 消费记录
- **消费明细**: 详细的消费记录查询
- **消费统计**: 消费数据统计分析
- **消费类型管理**: 不同消费类型分类管理

### ⚙️ 系统配置
- **价格配置**: 灵活的价格设置功能
- **系统参数**: 系统运行参数配置
- **权限配置**: 角色权限配置管理

### 🌍 地区管理
- **三级联动**: 省市县三级地区数据
- **数据同步**: 支持从公共API同步地区数据
- **地区搜索**: 快速地区搜索功能

## 🛠️ 技术栈

### 后端技术
- **框架**: Spring Boot 2.7.x
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **ORM**: MyBatis Plus
- **文档**: Swagger 3.x
- **构建**: Maven 3.x

### 前端技术
- **框架**: Vue.js 2.x
- **UI组件**: Element UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建**: Webpack

### 部署技术
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **CI/CD**: 支持自动化部署脚本

## 📦 项目结构

```
recruitment-platform/
├── recruitment-api/          # 后端项目
│   ├── ruoyi-admin/          # 管理模块
│   ├── ruoyi-common/         # 公共模块
│   ├── ruoyi-framework/      # 框架模块
│   ├── ruoyi-generator/      # 代码生成
│   ├── ruoyi-quartz/         # 定时任务
│   ├── ruoyi-system/         # 系统模块
│   ├── ruoyi-recruitment/    # 招聘业务模块
│   └── ruoyi-ui/            # 前端项目
├── deployment/               # 部署配置
│   ├── docker-compose.yml   # Docker编排文件
│   ├── deploy.sh            # 部署脚本
│   └── nginx/               # Nginx配置
├── sql/                     # 数据库脚本
│   ├── recruitment_tables.sql  # 数据表结构
│   ├── recruitment_menu.sql    # 菜单权限
│   └── recruitment_roles.sql   # 角色配置
└── docs/                    # 项目文档
```

## 🚀 快速开始

### 环境要求
- JDK 8+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/your-repo/recruitment-platform.git
cd recruitment-platform
```

2. **数据库初始化**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ry_vue CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据
mysql -u root -p ry_vue < sql/ry_2021xxxx.sql
mysql -u root -p ry_vue < sql/recruitment_tables.sql
mysql -u root -p ry_vue < sql/recruitment_menu.sql
mysql -u root -p ry_vue < sql/recruitment_roles.sql
```

3. **后端启动**
```bash
cd recruitment-api
mvn clean install
cd ruoyi-admin
mvn spring-boot:run
```

4. **前端启动**
```bash
cd recruitment-api/ruoyi-ui
npm install
npm run dev
```

5. **访问系统**
- 前端地址: http://localhost:80
- 后端地址: http://localhost:8080
- 接口文档: http://localhost:8080/swagger-ui.html

### Docker部署

1. **使用部署脚本**
```bash
cd deployment
chmod +x deploy.sh
./deploy.sh prod build    # 构建项目
./deploy.sh prod start    # 启动服务
```

2. **手动部署**
```bash
cd deployment
docker-compose up -d
```

## 👥 默认账户

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 超级管理员 | admin | admin123 | 拥有所有权限 |
| 招聘管理员 | recruitment | admin123 | 招聘模块管理权限 |
| 商家用户 | merchant | admin123 | 商家功能权限 |
| 求职用户 | jobseeker | admin123 | 求职功能权限 |

## 📖 使用说明

### 管理员操作
1. 登录管理后台
2. 在"招聘管理"菜单下管理各个功能模块
3. 可以审核商家、职位，管理用户等

### 商家操作
1. 注册商家账户并等待审核
2. 审核通过后可以发布职位
3. 管理收到的求职申请
4. 查看订单和消费记录

### 求职者操作
1. 注册个人账户
2. 完善个人档案和简历
3. 搜索并申请心仪职位
4. 购买VIP获得更多特权

## 🔧 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: **********************************
    username: root
    password: password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: password
```

### 文件上传配置
```yaml
ruoyi:
  profile: /path/to/upload
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: https://github.com/your-repo/recruitment-platform
- 问题反馈: https://github.com/your-repo/recruitment-platform/issues
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- [RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue) - 基础框架
- [Element UI](https://element.eleme.io/) - UI组件库
- [Spring Boot](https://spring.io/projects/spring-boot) - 后端框架
- [Vue.js](https://vuejs.org/) - 前端框架
