version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: recruitment-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: ry-vue
      MYSQL_USER: recruitment
      MYSQL_PASSWORD: recruitment123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - recruitment-network

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: recruitment-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass "redis123456"
    networks:
      - recruitment-network

  # 后端API服务
  recruitment-api:
    build:
      context: ../recruitment-api
      dockerfile: Dockerfile
    container_name: recruitment-api
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=*******************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=recruitment
      - SPRING_DATASOURCE_PASSWORD=recruitment123
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=redis123456
    depends_on:
      - mysql
      - redis
    volumes:
      - api_logs:/app/logs
      - api_upload:/app/uploadPath
    networks:
      - recruitment-network

  # 前端Web服务
  recruitment-web:
    build:
      context: ../recruitment-api/ruoyi-ui
      dockerfile: Dockerfile
    container_name: recruitment-web
    restart: always
    ports:
      - "80:80"
    depends_on:
      - recruitment-api
    networks:
      - recruitment-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: recruitment-nginx
    restart: always
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - recruitment-web
      - recruitment-api
    networks:
      - recruitment-network

volumes:
  mysql_data:
  redis_data:
  api_logs:
  api_upload:
  nginx_logs:

networks:
  recruitment-network:
    driver: bridge
