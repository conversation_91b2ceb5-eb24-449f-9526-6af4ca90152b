#!/bin/bash

# 招聘平台部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|test|prod
# 操作: build|start|stop|restart|logs|clean

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.yml"

# 默认参数
ENVIRONMENT=${1:-dev}
ACTION=${2:-start}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    # 构建后端
    log_info "构建后端项目..."
    cd "$PROJECT_ROOT/recruitment-api"
    if [ -f "pom.xml" ]; then
        mvn clean package -DskipTests
        log_success "后端构建完成"
    else
        log_warning "未找到pom.xml，跳过后端构建"
    fi
    
    # 构建前端
    log_info "构建前端项目..."
    cd "$PROJECT_ROOT/recruitment-api/ruoyi-ui"
    if [ -f "package.json" ]; then
        npm install
        npm run build:prod
        log_success "前端构建完成"
    else
        log_warning "未找到package.json，跳过前端构建"
    fi
    
    cd "$SCRIPT_DIR"
    log_success "项目构建完成"
}

# 启动服务
start_services() {
    log_info "启动招聘平台服务..."
    
    # 创建必要的目录
    mkdir -p "$SCRIPT_DIR/sql"
    mkdir -p "$SCRIPT_DIR/nginx"
    
    # 复制SQL文件
    if [ -d "$PROJECT_ROOT/sql" ]; then
        cp -r "$PROJECT_ROOT/sql"/* "$SCRIPT_DIR/sql/"
        log_info "SQL文件已复制"
    fi
    
    # 启动服务
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_success "服务启动完成"
    log_info "访问地址: http://localhost"
    log_info "管理后台: http://localhost/login"
    log_info "API文档: http://localhost:8080/swagger-ui.html"
}

# 停止服务
stop_services() {
    log_info "停止招聘平台服务..."
    docker-compose -f "$COMPOSE_FILE" down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启招聘平台服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose -f "$COMPOSE_FILE" logs -f
}

# 清理资源
clean_resources() {
    log_warning "这将删除所有容器、镜像和数据卷，是否继续？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理Docker资源..."
        docker-compose -f "$COMPOSE_FILE" down -v --rmi all
        docker system prune -f
        log_success "资源清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查MySQL
    if docker-compose -f "$COMPOSE_FILE" exec mysql mysqladmin ping -h localhost --silent; then
        log_success "MySQL服务正常"
    else
        log_error "MySQL服务异常"
    fi
    
    # 检查Redis
    if docker-compose -f "$COMPOSE_FILE" exec redis redis-cli ping | grep -q PONG; then
        log_success "Redis服务正常"
    else
        log_error "Redis服务异常"
    fi
    
    # 检查API服务
    if curl -f http://localhost:8080/actuator/health &> /dev/null; then
        log_success "API服务正常"
    else
        log_error "API服务异常"
    fi
    
    # 检查Web服务
    if curl -f http://localhost &> /dev/null; then
        log_success "Web服务正常"
    else
        log_error "Web服务异常"
    fi
}

# 备份数据
backup_data() {
    log_info "备份数据库..."
    BACKUP_DIR="$SCRIPT_DIR/backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    docker-compose -f "$COMPOSE_FILE" exec mysql mysqldump -u root -proot123456 ry-vue > "$BACKUP_DIR/database.sql"
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 恢复数据
restore_data() {
    if [ -z "$3" ]; then
        log_error "请指定备份文件路径"
        exit 1
    fi
    
    BACKUP_FILE="$3"
    if [ ! -f "$BACKUP_FILE" ]; then
        log_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    log_info "恢复数据库..."
    docker-compose -f "$COMPOSE_FILE" exec -T mysql mysql -u root -proot123456 ry-vue < "$BACKUP_FILE"
    log_success "数据恢复完成"
}

# 显示帮助信息
show_help() {
    echo "招聘平台部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作] [参数]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境"
    echo "  test    测试环境"
    echo "  prod    生产环境"
    echo ""
    echo "操作:"
    echo "  build     构建项目"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  logs      查看日志"
    echo "  clean     清理资源"
    echo "  health    健康检查"
    echo "  backup    备份数据"
    echo "  restore   恢复数据 [备份文件路径]"
    echo "  help      显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 prod build     # 构建生产环境"
    echo "  $0 dev start      # 启动开发环境"
    echo "  $0 prod backup    # 备份生产环境数据"
}

# 主函数
main() {
    log_info "招聘平台部署脚本 - 环境: $ENVIRONMENT, 操作: $ACTION"
    
    case $ACTION in
        build)
            check_requirements
            build_project
            ;;
        start)
            check_requirements
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_resources
            ;;
        health)
            health_check
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$@"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
